package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.common.activiti.pojo.entity.SysAdmin;
import com.cohen.common.activiti.pojo.entity.SysListener;
import com.cohen.common.activiti.pojo.entity.WxUser;
import com.cohen.common.activiti.service.impl.WxUserServiceImpl;

/**
 * 微信用户
 * <AUTHOR>
 * @create 2024-06-05
 */
public interface WxUserService extends IService<WxUser> {

    /**
     * 查询用户信息
     * @param userId
     * @return
     */
    WxUser selectUserById(Long userId);
}
