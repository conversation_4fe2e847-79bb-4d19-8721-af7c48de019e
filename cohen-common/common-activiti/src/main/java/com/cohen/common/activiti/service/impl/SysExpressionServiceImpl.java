package com.cohen.common.activiti.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.activiti.mapper.SysExpressionMapper;
import com.cohen.common.activiti.pojo.entity.SysExpression;
import com.cohen.common.activiti.pojo.form.SysExpressionForm;
import com.cohen.common.activiti.pojo.po.SysExpressionPO;
import com.cohen.common.activiti.pojo.query.SysExpressionPageQuery;
import com.cohen.common.activiti.pojo.vo.SysExpressionVO;
import com.cohen.common.activiti.service.SysExpressionService;
import com.cohen.common.activiti.converter.SysExpressionConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;



/**
 * 流程达式Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
@RequiredArgsConstructor
public class SysExpressionServiceImpl extends ServiceImpl<SysExpressionMapper, SysExpression>implements SysExpressionService {

    private final SysExpressionConverter SysExpressionConverter;


    @Override
    public IPage<SysExpressionVO> listSysExpressionPages(SysExpressionPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<SysExpressionPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<SysExpressionPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<SysExpressionVO> result = SysExpressionConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public SysExpressionVO getSysExpressionData(Long id) {
        SysExpression sd = this.getById(id);
        SysExpressionVO SysExpressionVO = SysExpressionConverter.entity2Vo(sd);
        return SysExpressionVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveSysExpression(SysExpressionForm form) {
        SysExpression SysExpression = SysExpressionConverter.form2Entity(form);
        boolean result = this.save(SysExpression);
        return result;
    }

    @Override
    public boolean updateSysExpression(Long id, SysExpressionForm form) {
        SysExpression ahDoctor = SysExpressionConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteSysExpression(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
