
package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.common.activiti.pojo.entity.SysListener;
import com.cohen.common.activiti.pojo.form.SysListenerForm;
import com.cohen.common.activiti.pojo.query.SysListenerPageQuery;
import com.cohen.common.activiti.pojo.vo.SysListenerVO;


/**
 * 流程监听Service接口
 *
 * <AUTHOR>
 * @date 2024-05-16
 */

public interface SysListenerService extends IService<SysListener> {

    /**
     * 分頁列表
     * @return
     */
    IPage<SysListenerVO> listSysListenerPages(SysListenerPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    SysListenerVO getSysListenerData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveSysListener(SysListenerForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateSysListener(Long id, SysListenerForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteSysListener(String idsStr);
}


