package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.activiti.pojo.query.ProcessDefinitionPageQuery;
import com.cohen.common.activiti.pojo.vo.ProcessDefinitionVO;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-15
 */
public interface ProcessDefinitionService {

    /**
     * 查询流程定义分页列表
     * @param queryParams
     * @return
     */
    IPage<ProcessDefinitionVO> lisProcessDefinitionPages(ProcessDefinitionPageQuery queryParams);

    /**
     * 保存流程设计器内的xml文件
     * @param name
     * @param category
     * @param in
     */
    void importFile(String name, String category, InputStream in);

    /**
     * 读取xml文件
     * @param deployId
     * @return
     */
    String readXml(String deployId) throws IOException;

    /**
     * 读取图片文件
     * @param deployId
     * @return
     */
    InputStream readImage(String deployId);

    /**
     * 激活或挂起流程定义
     * @param state
     * @param deployId
     */
    void updateState(Integer state, String deployId);

    /**
     * 删除流程定义
     * @param deploymentId
     */
    void deleteProcessDefinitionById(String deploymentId);

    /**
     * 根据流程定义Key启动流程实例
     * @param procDefKey
     * @param variables
     */
    void startProcessInstanceById(String procDefKey,Long businessId, Map<String, Object> variables);
}
