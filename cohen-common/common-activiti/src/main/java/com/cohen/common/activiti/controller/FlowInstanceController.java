package com.cohen.common.activiti.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.activiti.pojo.query.FlowInstancePageQuery;
import com.cohen.common.activiti.pojo.vo.FlowInstanceInfoVO;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import com.cohen.common.activiti.service.FlowInstanceService;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024-05-13
 */
@Slf4j
@Api(tags = "流程实例管理")
@RestController
@RequestMapping("/api/v1/flowable/instance")
@RequiredArgsConstructor
public class FlowInstanceController {

    private final FlowInstanceService flowInstanceService;



    @ApiOperation(value = "查询所有正在运行的流程实例列表")
    @GetMapping("/listProcess")
    public PageResult getFlowInstancePages(FlowInstancePageQuery queryParams){
        IPage<FlowInstanceInfoVO> result = flowInstanceService.getFlowInstancePages(queryParams);
        return PageResult.success(result);
    }

//    @ApiOperation(value = "根据流程定义id启动流程实例")
//    @PostMapping("/startBy/{procDefId}")
//    public Result startById(@ApiParam(value = "流程定义id") @PathVariable(value = "procDefId") String procDefId,
//                            @ApiParam(value = "变量集合,json对象") @RequestBody Map<String, Object> variables) {
//        return flowInstanceService.startProcessInstanceById(procDefId, variables);
//    }


    @ApiOperation(value = "挂起一个流程实例")
    @PutMapping("/suspend/{processInstanceId}")
    public Result suspend(@PathVariable String processInstanceId){
        flowInstanceService.suspend(processInstanceId);
        return Result.judge(true);
    }

    @ApiOperation(value = "唤醒一个挂起的流程实例")
    @PutMapping(value = "/run/{processInstanceId}")
    public Result rerun(@PathVariable String processInstanceId) {
        flowInstanceService.rerun(processInstanceId);
        return Result.judge(true);
    }


    @ApiOperation(value = "结束流程实例")
    @PutMapping("/stopProcessInstance/{procInsId}")
    public Result stopProcessInstance(@PathVariable String procInsId,
                                      @RequestParam(required = false) String reason) {
        flowInstanceService.stopProcessInstance(procInsId,reason);
        return Result.judge(true);
    }


    @ApiOperation(value = "删除流程实例")
    @DeleteMapping(value = "/delete/{procInsId}")
    public Result delete(@PathVariable String procInsId,
                         @RequestParam(required = false) String reason) {
        flowInstanceService.delete(procInsId,reason);
        return Result.judge(true);
    }

    @ApiOperation(value = "查询流程实例的活动历史")
    @GetMapping("/historyFlowInstance")
    public PageResult historyFlowInstance(FlowInstancePageQuery queryParams){
        IPage<TaskInfoVO> result = flowInstanceService.historyFlowInstance(queryParams);
        return PageResult.success(result);
    }


    @ApiOperation(value = "查询历史流程实例")
    @GetMapping("/getHistoryProcessInstance")
    public PageResult queryHistoryProcessInstance(FlowInstancePageQuery queryParams){
        IPage<FlowInstanceInfoVO> result = flowInstanceService.queryHistoryProcessInstance(queryParams);
        return PageResult.success(result);
    }
}
