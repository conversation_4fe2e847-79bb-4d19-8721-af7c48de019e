
package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.common.activiti.pojo.entity.SysForm;
import com.cohen.common.activiti.pojo.form.SysFormForm;
import com.cohen.common.activiti.pojo.query.SysFormPageQuery;
import com.cohen.common.activiti.pojo.vo.SysFormVO;


/**
 * 流程单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-23
 */

public interface SysFormService extends IService<SysForm> {

    /**
     * 分頁列表
     * @return
     */
    IPage<SysFormVO> listSysFormPages(SysFormPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    SysFormVO getSysFormData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveSysForm(SysFormForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateSysForm(Long id, SysFormForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteSysForm(String idsStr);
}


