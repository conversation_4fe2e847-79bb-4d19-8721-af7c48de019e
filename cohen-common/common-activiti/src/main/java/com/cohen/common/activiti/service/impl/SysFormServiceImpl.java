package com.cohen.common.activiti.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.activiti.converter.SysFormConverter;
import com.cohen.common.activiti.mapper.SysFormMapper;
import com.cohen.common.activiti.pojo.entity.SysForm;
import com.cohen.common.activiti.pojo.form.SysFormForm;
import com.cohen.common.activiti.pojo.po.SysFormPO;
import com.cohen.common.activiti.pojo.query.SysFormPageQuery;
import com.cohen.common.activiti.pojo.vo.SysFormVO;
import com.cohen.common.activiti.service.SysFormService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;



/**
 * 流程单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
@RequiredArgsConstructor
public class SysFormServiceImpl extends ServiceImpl<SysFormMapper, SysForm>implements SysFormService {

    private final SysFormConverter sysFormConverter;


    @Override
    public IPage<SysFormVO> listSysFormPages(SysFormPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<SysFormPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<SysFormPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<SysFormVO> result = sysFormConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public SysFormVO getSysFormData(Long id) {
        SysForm sd = this.getById(id);
        SysFormVO SysFormVO = sysFormConverter.entity2Vo(sd);
        return SysFormVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveSysForm(SysFormForm form) {
        SysForm SysForm = sysFormConverter.form2Entity(form);
        boolean result = this.save(SysForm);
        return result;
    }

    @Override
    public boolean updateSysForm(Long id, SysFormForm form) {
        SysForm ahDoctor = sysFormConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteSysForm(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
