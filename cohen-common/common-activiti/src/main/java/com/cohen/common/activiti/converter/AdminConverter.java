package com.cohen.common.activiti.converter;


import com.cohen.common.activiti.pojo.entity.SysAdmin;
import com.cohen.common.activiti.pojo.form.AdminForm;
import com.cohen.common.activiti.pojo.vo.AdminVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 用户对象转换器
 *
 */
@Mapper(componentModel = "spring")
public interface AdminConverter {

    @InheritInverseConfiguration(name = "entity2Form")
    SysAdmin form2Entity(AdminForm entity);

    AdminVO entity2VO(SysAdmin entity);
}
