package com.cohen.common.activiti.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.entity.SysExpression;
import com.cohen.common.activiti.pojo.po.SysExpressionPO;
import com.cohen.common.activiti.pojo.query.SysExpressionPageQuery;
import org.apache.ibatis.annotations.Mapper;

/**
 * 流程达式Mapper
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Mapper
public interface SysExpressionMapper extends BaseMapper<SysExpression> {

    /**
     * 分页查询
     * @param page
     * @param queryParams
     * @return
    */

    Page<SysExpressionPO> listPages(Page<SysExpressionPO> page, SysExpressionPageQuery queryParams);


}

