package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.form.FlowTaskForm;
import com.cohen.common.activiti.pojo.query.FlowPageQuery;
import com.cohen.common.activiti.pojo.query.TaskPageQuery;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.task.Task;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-15
 */
public interface FlowTaskService {


    /**
     * 审批任务
     *
     * @param task 请求实体参数
     */
    void complete(FlowTaskForm task);

//
//    /***
//     * 驳回任务
//     * @param form
//     */
//    void taskReject(FlowTaskForm form);

    /**
     * 查询我的待办任务列表
     *
     * @param queryParams  请求参数
     * @return
     */
    IPage<TaskInfoVO> myList(TaskPageQuery queryParams);

    /**
     * 已办任务列表
     *
     * @param queryParams  请求参数
     * @return
     */
    IPage<TaskInfoVO>  finishedList(TaskPageQuery queryParams);


    /**
     * 获取流程变量
     * @param taskId
     * @return
     */
    Map<String,Object> processVariables(String taskId);

    /**
     * 认领/签收任务
     *
     * @param form 请求实体参数
     */
    void claim(FlowTaskForm form);

    /**
     * 取消认领/签收任务
     *
     * @param form 请求实体参数
     */
    void unClaim(FlowTaskForm form);

    /**
     * 委派任务
     *
     * @param form 请求实体参数
     */
    void delegateTask(FlowTaskForm form);

    /**
     * 任务归还
     *
     * @param form 请求实体参数
     */
    void resolveTask(FlowTaskForm form);

    /**
     * 转办任务
     *
     * @param form 请求实体参数
     */
    void assignTask(FlowTaskForm form);

    /**
     * 查询历史任务分页列表
     * @param queryParams
     * @return
     */
    IPage<TaskInfoVO> queryHistoryTask(TaskPageQuery queryParams);


    /**
     *  根据流程定义id和业务数据id查询当前流程实例的节点
     * @param businessId
     * @param procInsId
     * @return
     */
     Task getTaskByBusinessId(String businessId, String procInsId);



    /**
     * 流程图进度追踪
     * @param instanceId
     * @param response
     */
    void traceProcess(String instanceId, HttpServletResponse response);

    /**
     * 退回任务
     * @param flowTaskVo
     */
    void taskReturn(FlowTaskForm flowTaskVo);

    /**
     * 获取所有可回退的节点
     * @param form
     * @return
     */
    List<UserTask> findReturnTaskList(FlowTaskForm form);

    /**
     * 跳转到指定节点
     * @param taskId
     * @param sid
     */
    void jump(String taskId, String sid);

    /**
     * 驳回任务
     * @param flowTaskForm
     */
    void taskReject(FlowTaskForm flowTaskForm);

    /**
     * 获取流程过程图
     * @param processId
     * @return
     */
    InputStream diagram(String processId);


    /**
     * 我发起的流程
     * @param queryParams  请求参数
     * @return
     */
    IPage<TaskInfoVO> myProcess(FlowPageQuery queryParams);

/*    *//**
     * 取消申请
     * 目前实现方式: 直接将当前流程实例删除
     * @param form
     *//*
    void stopProcess(FlowTaskForm form);*/

    /**
     * 撤销:强制结束一个流程
     * @param taskId
     */
    void forceEnd(String taskId);
}
