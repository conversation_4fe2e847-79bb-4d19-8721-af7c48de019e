package com.cohen.common.activiti.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.entity.SysAdmin;
import com.cohen.common.activiti.pojo.entity.WxUser;
import com.cohen.common.activiti.pojo.query.FlowInstancePageQuery;
import com.cohen.common.activiti.pojo.vo.FlowInstanceInfoVO;
import com.cohen.common.activiti.constant.ProcessConstants;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import com.cohen.common.activiti.service.FlowInstanceService;
import com.cohen.common.activiti.service.SysAdminService;
import com.cohen.common.activiti.service.WxUserService;
import com.cohen.common.activiti.utils.DateUtil;
import com.cohen.common.result.Result;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.web.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.HistoryService;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.*;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-05-13
 */
@Service
@RequiredArgsConstructor
public class FlowInstanceServiceImpl implements FlowInstanceService {

    private final RuntimeService runtimeService;

    private final TaskService taskService;

    private final HistoryService historyService;
    private final IdentityService identityService;

    private final SysAdminService sysAdminService;

    private final WxUserService wxUserService;

    /**
     * 查询所有正在运行的流程实例列表
     *
     * @param queryParams
     * @return
     */
    @Override
    public IPage<FlowInstanceInfoVO> getFlowInstancePages(FlowInstancePageQuery queryParams) {
        Page<FlowInstanceInfoVO> result = new Page<>();
        ProcessInstanceQuery condition = runtimeService.createProcessInstanceQuery();
        if (StringUtils.isNoneEmpty(queryParams.getName())) {
            condition.processInstanceNameLike("%"+queryParams.getName()+"%");
        }
        if (StringUtils.isNoneEmpty(queryParams.getBussinesskey())) {
            condition.processInstanceId(queryParams.getBussinesskey());
        }
        if (StringUtils.isNoneEmpty(queryParams.getStartUserName())) {
            condition.variableValueLike(ProcessConstants.PROCESS_INITIATOR_NAME, "%" + queryParams.getStartUserName() + "%");
        }
        int start = (queryParams.getPageNum() - 1) * queryParams.getPageSize();
        int total = condition.orderByProcessDefinitionId().desc().list().size();
        List<ProcessInstance> processList = condition.orderByProcessDefinitionId().desc().listPage(start, queryParams.getPageSize());
        ArrayList<FlowInstanceInfoVO> flows = new ArrayList<>();
        processList.stream().forEach(p -> {
            FlowInstanceInfoVO info = new FlowInstanceInfoVO();
            info.setProcessInstanceId(p.getProcessInstanceId());
            info.setBusinessKey(p.getBusinessKey());
            info.setName(p.getProcessDefinitionName());
            info.setStartTime(p.getStartTime());
            info.setStartUserId(p.getStartUserId());
            info.setSuspended(p.isSuspended());
            info.setEnded(p.isEnded());

            //查询该流程实例存储的变量信息
            Map<String, Object> variables = runtimeService.getVariables(p.getProcessInstanceId());
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR_NAME)) {
                String initiatorName = (String) variables.get(ProcessConstants.PROCESS_INITIATOR_NAME);
                info.setStartUserName(initiatorName);
            }
            if (variables.containsKey(ProcessConstants.BUSINESS_TITLE)){
                String businessTitle =  (String) variables.get(ProcessConstants.BUSINESS_TITLE);
                info.setBusinessTitle(businessTitle);
            }
            // 查看当前活动任务
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(p.getProcessInstanceId()).list();
            if (CollectionUtils.isNotEmpty(taskList)) {
                info.setTaskId(taskList.get(0).getId());
                info.setCurrentTask(taskList.get(0).getName());
                if (StringUtils.isNotBlank(taskList.get(0).getAssignee())) {
                    String assignee = taskList.get(0).getAssignee();
                    if (assignee.contains("wx_")){//判断是否为微信用户
                        String wxUserId = assignee.replace("wx_", "");
                        WxUser wxUser = wxUserService.selectUserById(Long.parseLong(wxUserId));
                        if (Objects.nonNull(wxUser)) {
                            info.setAssigneeId(wxUser.getId().toString());
                            info.setAssigneeName(wxUser.getNickname());
                        }
                    }else {
                        // 当前任务节点办理人信息
                        SysAdmin sysUser = sysAdminService.selectUserById(Long.parseLong(taskList.get(0).getAssignee()));
                        if (Objects.nonNull(sysUser)) {
                            info.setAssigneeId(sysUser.getId().toString());
                            info.setAssigneeName(sysUser.getNickname());
                        }
                    }
                }
            } else {
                List<HistoricTaskInstance> historicTaskInstance = historyService.createHistoricTaskInstanceQuery().processInstanceId(p.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
                info.setTaskId(historicTaskInstance.get(0).getId());
                info.setCurrentTask(historicTaskInstance.get(0).getName());
                if (StringUtils.isNotBlank(historicTaskInstance.get(0).getAssignee())) {
                    // 当前任务节点办理人信息
                    String assignee = taskList.get(0).getAssignee();
                    if (assignee.contains("wx_")){//判断是否为微信用户
                        String wxUserId = assignee.replace("wx_", "");
                        WxUser wxUser = wxUserService.selectUserById(Long.parseLong(wxUserId));
                        if (Objects.nonNull(wxUser)) {
                            info.setAssigneeId(wxUser.getId().toString());
                            info.setAssigneeName(wxUser.getNickname());
                        }
                    }else {
                        SysAdmin sysUser = sysAdminService.selectUserById(Long.parseLong(historicTaskInstance.get(0).getAssignee()));
                        if (Objects.nonNull(sysUser)) {
                            info.setAssigneeId(sysUser.getId().toString());
                            info.setAssigneeName(sysUser.getNickname());
                        }
                    }
                }
            }
            flows.add(info);
        });
        result.setTotal(total);
        result.setRecords(flows);
        return result;
    }

    /**
     * 挂起一个流程实例
     *
     * @param processInstanceId
     */
    @Override
    public void suspend(String processInstanceId) {
        runtimeService.suspendProcessInstanceById(processInstanceId);
    }

    /**
     * 唤醒一个挂起的流程实例
     *
     * @param processInstanceId
     */
    @Override
    public void rerun(String processInstanceId) {
        runtimeService.activateProcessInstanceById(processInstanceId);
    }

    /**
     * 删除流程实例
     *
     * @param processInstanceId
     * @param reason
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String processInstanceId, String reason) {
        // 查询历史数据
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(processInstanceId);
        if (historicProcessInstance.getEndTime() != null) {
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getId());
            return;
        }
        // 删除流程实例
        runtimeService.deleteProcessInstance(processInstanceId, reason);
        // 删除历史流程实例
        historyService.deleteHistoricProcessInstance(processInstanceId);
    }

    /**
     * 根据实例ID查询历史实例数据
     *
     * @param processInstanceId
     * @return
     */
    public HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (Objects.isNull(historicProcessInstance)) {
            throw new BizException("流程实例不存在: " + processInstanceId);
        }
        return historicProcessInstance;
    }


    /**
     * 查询流程实例的活动历史
     *
     * @param queryParams
     * @return
     */
    @Override
    public IPage<TaskInfoVO> historyFlowInstance(FlowInstancePageQuery queryParams) {
        Page<TaskInfoVO> result = new Page<>();
        String processInstanceId = queryParams.getProcessInstanceId();
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        int start = (pageNum - 1) * pageSize;
        List<HistoricActivityInstance> history = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .activityType("userTask") //只查询userTask节点的活动历史
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .listPage(start, pageSize);

        int total = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .activityType("userTask")
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list()
                .size();

        List<TaskInfoVO> infos = new ArrayList<>();
        history.stream().forEach(h -> {
            TaskInfoVO info = new TaskInfoVO();
            info.setProcessInstanceId(h.getProcessInstanceId());
            info.setStartTime(h.getStartTime());
            if (h.getEndTime() != null) {
                info.setEndTime(h.getEndTime());
                long time = h.getEndTime().getTime() - h.getStartTime().getTime();
                info.setDuration(DateUtil.getDate(time));
            }
            info.setAssigneeName(h.getAssignee());
            info.setTaskName(h.getActivityName());
            List<Comment> comments = taskService.getTaskComments(h.getTaskId());
            if (comments.size() > 0) {
                info.setComment(comments.get(0).getFullMessage());
            }
            infos.add(info);
        });
        result.setTotal(total);
        result.setRecords(infos);
        return result;
    }

    /**
     * 查询历史流程实例
     *
     * @param queryParams
     * @return
     */
    @Override
    public IPage<FlowInstanceInfoVO> queryHistoryProcessInstance(FlowInstancePageQuery queryParams) {
        Page<FlowInstanceInfoVO> result = new Page<>();
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        pageNum = (pageNum - 1) * pageSize;
        HistoricProcessInstanceQuery processInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        //已完成的历史流程实例
        processInstanceQuery.finished();
        if (StringUtils.isNoneEmpty(queryParams.getName())) {
            processInstanceQuery.processInstanceNameLike("%" + queryParams.getName() + "%");
        }
        if (StringUtils.isNoneEmpty(queryParams.getProcessInstanceId())) {
            processInstanceQuery.processInstanceId(queryParams.getProcessInstanceId());
        }
        if (StringUtils.isNoneEmpty(queryParams.getStartUserName())) {
            processInstanceQuery.variableValueLike(ProcessConstants.PROCESS_INITIATOR_NAME, "%" + queryParams.getStartUserName() + "%");
        }
        int total = processInstanceQuery.orderByProcessInstanceStartTime().desc().list().size();
        List<HistoricProcessInstance> historicProcIns = processInstanceQuery
                .orderByProcessInstanceStartTime()
                .desc()
                .listPage(pageNum, pageSize);
        ArrayList<FlowInstanceInfoVO> list = new ArrayList<>();
        historicProcIns.stream().forEach(h -> {
            FlowInstanceInfoVO instanceInfo = new FlowInstanceInfoVO();
            instanceInfo.setName(h.getName());
            instanceInfo.setProcessInstanceId(h.getId());
            instanceInfo.setProcDefId(h.getProcessDefinitionId());
            instanceInfo.setProcDefName(h.getProcessDefinitionName());
            instanceInfo.setStartTime(h.getStartTime());
            if (h.getEndTime() != null) {
                instanceInfo.setEndTime(h.getEndTime());
                long time = h.getEndTime().getTime() - h.getStartTime().getTime();
                instanceInfo.setDuration(DateUtil.getDate(time));
            }
            //查询发起人
            Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(h.getId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(HistoricVariableInstance::getVariableName, HistoricVariableInstance::getValue));
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR)){
                Long startUserId = (Long) variables.get(ProcessConstants.PROCESS_INITIATOR);
                instanceInfo.setStartUserId(startUserId.toString());
            }
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR_NAME)){
                String startUserName = (String) variables.get(ProcessConstants.PROCESS_INITIATOR_NAME);
                instanceInfo.setStartUserName(startUserName);
            }
            if (variables.containsKey(ProcessConstants.BUSINESS_TITLE)){
                String businessTitle = (String) variables.get(ProcessConstants.BUSINESS_TITLE);
                instanceInfo.setBusinessTitle(businessTitle);
            }
            list.add(instanceInfo);
        });
        result.setTotal(total);
        result.setRecords(list);
        return result;
    }

    /**
     * 创建流程实例
     * @param procDefId
     * @param variables
     * @return
     */
    @Override
    public String startProcessInstanceById(String procDefId,String businessKey, Map<String, Object> variables) {
        try {
            // 设置流程发起人Id到流程中
            Long userId = SecurityUtils.getUserId();
            identityService.setAuthenticatedUserId(userId.toString());
            variables.put(ProcessConstants.PROCESS_INITIATOR,userId);
            variables.put(ProcessConstants.PROCESS_INITIATOR_NAME,SecurityUtils.getNickname());
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(procDefId, businessKey, variables);
//            runtimeService.startProcessInstanceByKey(procDefId,businessKey, variables);
            return processInstance.getProcessInstanceId();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("流程启动错误");
        }
    }

    /**
     * 结束流程实例
     * @param procInsId
     * @param reason
     */
    @Override
    public void stopProcessInstance(String procInsId, String reason) {
        runtimeService.deleteProcessInstance(procInsId,reason);
    }
}
