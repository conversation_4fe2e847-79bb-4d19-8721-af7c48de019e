package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.activiti.pojo.query.FlowInstancePageQuery;
import com.cohen.common.activiti.pojo.vo.FlowInstanceInfoVO;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import com.cohen.common.result.Result;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-15
 */
public interface FlowInstanceService {

    /**
     * 查询所有正在运行的流程实例列表
     * @param queryParams
     * @return
     */
    IPage<FlowInstanceInfoVO> getFlowInstancePages(FlowInstancePageQuery queryParams);

    /**
     * 根据流程定义id启动流程实例
     * @param procDefId  流程定义id
     * @param businessKey 业务id
     * @param variables 变量
     * @return
     */
    String startProcessInstanceById(String procDefId,String businessKey, Map<String, Object> variables);

    /**
     * 挂起一个流程实例
     * @param processInstanceId
     */
    void suspend(String processInstanceId);

    /**
     * 唤醒一个挂起的流程实例
     * @param processInstanceId
     */
    void rerun(String processInstanceId);

    /**
     * 结束流程实例
     * @param procInsId
     * @param reason
     */
    void stopProcessInstance(String procInsId, String reason);

    /**
     * 删除流程实例
     * @param procInsId
     * @param reason
     */
    void delete(String procInsId, String reason);

    /**
     * 查询流程实例的活动历史
     * @param queryParams
     * @return
     */
    IPage<TaskInfoVO> historyFlowInstance(FlowInstancePageQuery queryParams);

    /**
     * 查询历史流程实例
     * @param queryParams
     * @return
     */
    IPage<FlowInstanceInfoVO> queryHistoryProcessInstance(FlowInstancePageQuery queryParams);
}
