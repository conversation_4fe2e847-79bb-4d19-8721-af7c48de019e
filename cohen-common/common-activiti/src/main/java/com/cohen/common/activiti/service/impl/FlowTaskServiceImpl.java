package com.cohen.common.activiti.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.constant.ProcessConstants;
import com.cohen.common.activiti.enums.FlowComment;
import com.cohen.common.activiti.pojo.entity.SysAdmin;
import com.cohen.common.activiti.pojo.entity.WxUser;
import com.cohen.common.activiti.pojo.form.FlowTaskForm;
import com.cohen.common.activiti.pojo.query.FlowPageQuery;
import com.cohen.common.activiti.pojo.query.TaskPageQuery;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import com.cohen.common.activiti.service.FlowTaskService;
import com.cohen.common.activiti.service.SysAdminService;
import com.cohen.common.activiti.service.WxUserService;
import com.cohen.common.activiti.utils.ActivitiUtil;
import com.cohen.common.activiti.utils.CustomProcessDiagramGenerator;
import com.cohen.common.activiti.utils.DateUtil;
import com.cohen.common.activiti.utils.FlowableUtils;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.web.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.*;
import org.activiti.bpmn.model.Process;
import org.activiti.engine.*;
import org.activiti.engine.history.*;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.DelegationState;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.activiti.image.ProcessDiagramGenerator;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-05-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlowTaskServiceImpl implements FlowTaskService {

    private final HistoryService historyService;

    private final RepositoryService repositoryService;

    private final RuntimeService runtimeService;

    private final TaskService taskService;

    private final ProcessEngineConfiguration processEngineConfiguration;

    private final SysAdminService sysAdminService;
    private final WxUserService  wxUserService;


    /**
     * 完成任务
     *
     * @param taskForm 请求实体参数
     */
    @Transactional
    @Override
    public void complete(FlowTaskForm taskForm) {
        Task task = taskService.createTaskQuery().taskId(taskForm.getTaskId()).singleResult();
        if (Objects.isNull(task)) {
            throw new BizException("任务不存在");
        }
        if (DelegationState.PENDING.equals(task.getDelegationState())) {
            taskService.addComment(taskForm.getTaskId(), taskForm.getInstanceId(), FlowComment.DELEGATE.getType(), taskForm.getComment());
            taskService.resolveTask(taskForm.getTaskId(), taskForm.getVariables());
        } else {
            taskService.addComment(taskForm.getTaskId(), taskForm.getInstanceId(), FlowComment.NORMAL.getType(), taskForm.getComment());
            if (StringUtils.isEmpty(taskForm.getUserId())){
                taskForm.setUserId(SecurityUtils.getUserId().toString());
            }
            taskService.setAssignee(taskForm.getTaskId(), taskForm.getUserId());
            taskService.complete(taskForm.getTaskId(), taskForm.getVariables());
        }
    }

    /**
     * 查询我的待办任务列表
     *
     * @param queryParams 请求参数
     * @return
     */
    @Override
    public IPage<TaskInfoVO> myList(TaskPageQuery queryParams) {
        Page<TaskInfoVO> result = new Page<>();
        Long userId = SecurityUtils.getUserId();
//        Set<String> roles = SecurityUtils.getRoles();
        Set<String> roles = SecurityUtils.getRoleIds();
        // 过滤掉流程挂起的待办任务
        TaskQuery taskQuery = taskService.createTaskQuery();
        if (StringUtils.isNoneEmpty(queryParams.getName())) {
            taskQuery.taskNameLike("%" + queryParams.getName() + "*");
        }
        if (StringUtils.isNoneEmpty(queryParams.getProcessDefName())) {
            taskQuery.processDefinitionNameLike("%" + queryParams.getProcessDefName() + "%");
        }
        taskQuery = taskQuery.active()
                .includeProcessVariables()
                .taskCandidateGroupIn(roles.stream().collect(Collectors.toList()))
                .taskCandidateOrAssigned(userId.toString())
                .orderByTaskCreateTime().desc();
        result.setTotal(taskQuery.count());
        int pageNum = (queryParams.getPageNum() - 1) * queryParams.getPageSize();
        List<Task> taskList = taskQuery.listPage(pageNum, queryParams.getPageSize());

        ArrayList<TaskInfoVO> list = new ArrayList<>();
        taskList.stream().forEach(t -> {
            TaskInfoVO taskInfo = new TaskInfoVO();
            taskInfo.setTaskId(t.getId());
            taskInfo.setTaskName(t.getName());
            taskInfo.setTaskDefKey(t.getTaskDefinitionKey());
            taskInfo.setCreateTime(t.getCreateTime());
            taskInfo.setProcDefId(t.getProcessDefinitionId());
            taskInfo.setExecutionId(t.getExecutionId());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(t.getProcessDefinitionId())
                    .singleResult();
            taskInfo.setDeployId(pd.getDeploymentId());
            taskInfo.setProcDefName(pd.getName());
            taskInfo.setProcessInstanceId(t.getProcessInstanceId());
            // 流程实例
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(t.getProcessInstanceId())
                    .singleResult();
            taskInfo.setBusinessKey(processInstance.getBusinessKey());
            //获取流程发起人信息
            Map<String, Object> variables = runtimeService.getVariables(t.getProcessInstanceId());
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR)) {
//                Long initiator = (Long) variables.get(ProcessConstants.PROCESS_INITIATOR);
                Object initiator = variables.get(ProcessConstants.PROCESS_INITIATOR);
                taskInfo.setStartUserId(initiator.toString());
            }
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR_NAME)) {
                String initiatorName = (String) variables.get(ProcessConstants.PROCESS_INITIATOR_NAME);
                taskInfo.setStartUserName(initiatorName);
            }
            list.add(taskInfo);
        });
        result.setRecords(list);
        return result;
    }

    /**
     * 已办任务列表
     *
     * @param queryParams 请求参数
     * @return
     */
    @Override
    public IPage<TaskInfoVO> finishedList(TaskPageQuery queryParams) {
        Page<TaskInfoVO> result = new Page<>();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(SecurityUtils.getUserId().toString())
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        int pageNum = (queryParams.getPageNum() - 1) * queryParams.getPageSize();
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(pageNum, queryParams.getPageSize());
        ArrayList<TaskInfoVO> list = new ArrayList<>();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
            TaskInfoVO taskInfo = new TaskInfoVO();
            taskInfo.setTaskId(histTask.getId());
            taskInfo.setTaskName(histTask.getName());
            taskInfo.setCreateTime(histTask.getCreateTime());
            taskInfo.setStartTime(histTask.getStartTime());
            taskInfo.setEndTime(histTask.getEndTime());
            taskInfo.setDuration(DateUtil.getDate(histTask.getDurationInMillis()));
            taskInfo.setProcDefId(histTask.getProcessDefinitionId());
            taskInfo.setTaskDefKey(histTask.getTaskDefinitionKey());

            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(histTask.getProcessDefinitionId())
                    .singleResult();
            taskInfo.setDeployId(pd.getDeploymentId());
            taskInfo.setProcDefName(pd.getName());
            taskInfo.setProcessInstanceId(histTask.getProcessInstanceId());

            Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(HistoricVariableInstance::getVariableName, HistoricVariableInstance::getValue));
            //获取发起人信息
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR)) {
                Long initiator = (Long) variables.get(ProcessConstants.PROCESS_INITIATOR);
                taskInfo.setStartUserId(initiator.toString());
            }
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR_NAME)) {
                String initiatorName = (String) variables.get(ProcessConstants.PROCESS_INITIATOR_NAME);
                taskInfo.setStartUserName(initiatorName);
            }
            list.add(taskInfo);
        }
        result.setTotal(taskInstanceQuery.count());
        result.setRecords(list);
        return result;
    }

    /**
     * 获取流程变量
     *
     * @param taskId
     * @return
     */
    @Override
    public Map<String, Object> processVariables(String taskId) {
        // 流程变量
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskId(taskId)
                .singleResult();
        if (Objects.nonNull(historicTaskInstance)) {
            return historicTaskInstance.getProcessVariables();
        } else {
            Map<String, Object> variables = taskService.getVariables(taskId);
            return variables;
        }
    }


    /**
     * 认领/签收任务
     * 认领以后,这个用户就会成为任务的执行人,任务会从其他成员的任务列表中消失
     *
     * @param form 请求实体参数
     */
    @Override
    public void claim(FlowTaskForm form) {
        taskService.claim(form.getTaskId(), form.getUserId());
    }

    /**
     * 取消认领/签收任务
     *
     * @param form 请求实体参数
     */
    @Override
    public void unClaim(FlowTaskForm form) {
        taskService.unclaim(form.getTaskId());
    }

    /**
     * 委派任务
     * 任务委派只是委派人将当前的任务交给被委派人进行审批，处理任务后又重新回到委派人身上。
     *
     * @param form
     */
    @Override
    public void delegateTask(FlowTaskForm form) {
        taskService.delegateTask(form.getTaskId(), form.getAssignee());
    }

    /**
     * 任务归还
     * 被委派人完成任务之后，将任务归还委派人
     *
     * @param form
     */
    @Override
    public void resolveTask(FlowTaskForm form) {
        taskService.resolveTask(form.getTaskId());
    }

    /**
     * 转办任务
     * 直接将办理人换成别人，这时任务的拥有者不再是转办人
     *
     * @param form 请求实体参数
     */
    @Override
    public void assignTask(FlowTaskForm form) {
        // 直接转派就可以覆盖掉之前的
        taskService.setAssignee(form.getTaskId(), form.getAssignee());
    }

    /**
     * 查询历史任务分页列表
     *
     * @param queryParams
     * @return
     */
    @Override
    public IPage<TaskInfoVO> queryHistoryTask(TaskPageQuery queryParams) {
        Page<TaskInfoVO> result = new Page<>();
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        int start = (pageNum - 1) * pageSize;
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        //已完成的历史任务
        historicTaskInstanceQuery.finished();
        if (StringUtils.isNoneEmpty(queryParams.getName())) {
            historicTaskInstanceQuery.processDefinitionNameLike("%" + queryParams.getName() + "%");
        }
        if (StringUtils.isNoneEmpty(queryParams.getProcessInstanceId())) {
            historicTaskInstanceQuery.processInstanceId(queryParams.getProcessInstanceId());
        }
        if (StringUtils.isNoneEmpty(queryParams.getStartUserName())) {
            historicTaskInstanceQuery.processVariableValueLike(ProcessConstants.PROCESS_INITIATOR_NAME, "%" + queryParams.getStartUserName() + "%");
        }
        int total = historicTaskInstanceQuery.orderByHistoricTaskInstanceStartTime().desc().list().size();
        List<HistoricTaskInstance> historicTaskInstances = historicTaskInstanceQuery.orderByHistoricTaskInstanceStartTime().desc().listPage(start, pageSize);
        ArrayList<TaskInfoVO> list = new ArrayList<>();
        historicTaskInstances.stream().forEach(h -> {
            TaskInfoVO info = new TaskInfoVO();
            info.setTaskId(h.getId());
            info.setTaskName(h.getName());
            info.setExecutionId(h.getExecutionId());
            info.setTaskDefKey(h.getTaskDefinitionKey());
            info.setProcessInstanceId(h.getProcessInstanceId());
            info.setProcDefId(h.getProcessDefinitionId());
            info.setStartTime(h.getStartTime());
            info.setEndTime(h.getEndTime());
            if (h.getEndTime() != null) {
                Long time = h.getEndTime().getTime() - h.getStartTime().getTime();
                info.setDuration(DateUtil.getDate(time));
            }
            if (h.getAssignee()!=null){
                String assignee = h.getAssignee();
                if (assignee.contains("wx_")){
                    String wxUserid = assignee.replace("wx_", "");
                    WxUser wxUser = wxUserService.selectUserById(Long.parseLong(wxUserid));
                    if (Objects.nonNull(wxUser)){
                        info.setAssigneeId(Long.parseLong(wxUserid));
                        info.setAssigneeName(wxUser.getNickname());
                    }
                }else {
                    //查询任务处理人名称
                    SysAdmin sysAdmin = sysAdminService.selectUserById(Long.parseLong(assignee));
                    if (Objects.nonNull(sysAdmin)){
                        info.setAssigneeId(Long.parseLong(assignee));
                        info.setAssigneeName(sysAdmin.getNickname());
                    }
                }
            }
            //根据流程实例id查询流程实例名称
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(h.getProcessDefinitionId());
            if (processDefinition != null) {
                info.setProcDefName(processDefinition.getName());
                info.setCategory(processDefinition.getCategory());
            }
            //获取该流程实例的变量信息
            Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(h.getProcessInstanceId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(HistoricVariableInstance::getVariableName, HistoricVariableInstance::getValue));
            //获取发起人信息
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR)) {
                Long initiator = (Long) variables.get(ProcessConstants.PROCESS_INITIATOR);
                info.setStartUserId(initiator.toString());
            }
            if (variables.containsKey(ProcessConstants.PROCESS_INITIATOR_NAME)) {
                String initiatorName = (String) variables.get(ProcessConstants.PROCESS_INITIATOR_NAME);
                info.setStartUserName(initiatorName);
            }
            //获取业务标题
            if (variables.containsKey(ProcessConstants.BUSINESS_TITLE)){
                String businessTitle = (String) variables.get(ProcessConstants.BUSINESS_TITLE);
                info.setBusinessTitle(businessTitle);
            }
            list.add(info);
        });
        result.setTotal(total);
        result.setRecords(list);
        return result;
    }

    /**
     * 根据流程定义id和业务数据id查询当前流程实例的节点
     *
     * @param businessId
     * @param procInsId
     * @return
     */
    @Override
    public Task getTaskByBusinessId(String businessId, String procInsId) {
        // 查询流程实例
//        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(procInsId)
//                .processInstanceBusinessKey(businessId)
//                .singleResult();
//
//        //查询流程实例的当前任务节点
//        Task task = taskService.createTaskQuery()
//                .processInstanceId(procInsId)
//                .singleResult();
        Task task = taskService.createTaskQuery().processInstanceId(procInsId).singleResult();
        return task;
    }


    /**
     * 流程图进度追踪
     *
     * @param instanceId
     * @param response
     */
    @Override
    public void traceProcess(String instanceId, HttpServletResponse response) {
        if (StringUtils.isEmpty(instanceId)) {
            log.error("processInstanceId is null");
            return;
        }
        response.setContentType("image/jpg");
        // 获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(instanceId)
                .singleResult();
        // 获取流程中已经执行的节点
        List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(instanceId)
                .orderByHistoricActivityInstanceStartTime().asc().list();
        // 高亮已经执行流程节点ID集合
        List<String> highLightedActivitiIds = new ArrayList<>();
        for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
            highLightedActivitiIds.add(historicActivityInstance.getActivityId());
        }

        ProcessDiagramGenerator processDiagramGenerator = processEngineConfiguration.getProcessDiagramGenerator();

        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
        // 高亮流程已发生流转的线id集合
//        List<String> highLightedFlowIds = getHighLightedFlows(bpmnModel, historicActivityInstances);
        List<String> highLightedFlowIds = ActivitiUtil.getHighLightedFlows(bpmnModel, historicActivityInstances);

        String os = System.getProperty("os.name").toLowerCase();
        String font = "SimSun";
        if (os.startsWith("win")) {
            font = "宋体";
        }
        try (InputStream in = processDiagramGenerator.generateDiagram(bpmnModel, "jpg", highLightedActivitiIds, highLightedFlowIds,
                font, font, font, null, 2.0);
             BufferedInputStream bin = new BufferedInputStream(in);
             OutputStream out = response.getOutputStream();
             BufferedOutputStream bout = new BufferedOutputStream(out);
        ) {
            IOUtils.copy(bin, bout);
        } catch (Exception e) {
            log.error("processInstanceId" + instanceId + "生成流程图失败，原因：" + e.getMessage(), e);
        }
    }


    /**
     * 退回任务
     *
     * @param flowTaskVo
     */
    @Override
    public void taskReturn(FlowTaskForm flowTaskVo) {
        //获取待执行的任务节点
        Task task = taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult();
        if (task == null) {
            throw new BizException("当前任务节点不存在");
        }
        if (task.isSuspended()) {
            throw new BizException("任务处于挂起状态");
        }

        String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult().getProcessDefinitionId();
        //获取流程模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        //想要回退到的节点位置
        FlowNode myFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(flowTaskVo.getTargetKey());
        //原本的活动方向
        Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();
        FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId);
        //记录原活动方向
        List<SequenceFlow> oriSequenceFlows = new ArrayList<SequenceFlow>();
        oriSequenceFlows.addAll(flowNode.getOutgoingFlows());
        //清理活动方向
        flowNode.getOutgoingFlows().clear();
        //建立新方向
        List<SequenceFlow> newSequenceFlowList = new ArrayList<SequenceFlow>();
        SequenceFlow newSequenceFlow = new SequenceFlow();
        newSequenceFlow.setId("newFlow");
        //新方向的源头---当前节点
        newSequenceFlow.setSourceFlowElement(flowNode);
        //新方向的目标---要回退的节点
        newSequenceFlow.setTargetFlowElement(myFlowNode);
        newSequenceFlowList.add(newSequenceFlow);
        flowNode.setOutgoingFlows(newSequenceFlowList);

        Authentication.setAuthenticatedUserId(SecurityUtils.getUserId().toString());
        taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowComment.REBACK.getType(), "【回退】" + flowTaskVo.getComment());
        //完成任务
        System.out.println("========================完成任务====================");
        taskService.complete(task.getId());

        //恢复原方向
        flowNode.setOutgoingFlows(oriSequenceFlows);
        System.out.println("------------------RollBack successfully！！----------------------------");
        log.info("退回成功！");
    }

    /**
     * 获取所有可回退的节点
     *
     * @param form
     * @return
     */
    @Override
    public List<UserTask> findReturnTaskList(FlowTaskForm form) {
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(form.getTaskId()).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        // 获取所有节点信息，暂不考虑子流程情况
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        Collection<FlowElement> flowElements = process.getFlowElements();
        // 获取当前任务节点元素
        UserTask source = null;
        if (flowElements != null) {
            for (FlowElement flowElement : flowElements) {
                // 类型为用户节点
                if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    source = (UserTask) flowElement;
                }
            }
        }
        // 获取节点的所有路线
        List<List<UserTask>> roads = FlowableUtils.findRoad(source, null, null, null);
        // 可回退的节点列表
        List<UserTask> userTaskList = new ArrayList<>();
        for (List<UserTask> road : roads) {
            if (userTaskList.size() == 0) {
                // 还没有可回退节点直接添加
                userTaskList = road;
            } else {
                // 如果已有回退节点，则比对取交集部分
                userTaskList.retainAll(road);
            }
        }
        return userTaskList;
    }


    /**
     * 跳转到指定节点
     *
     * @param taskId
     * @param sid
     */
    @Override
    public void jump(String taskId, String sid) {
        Task t = taskService.createTaskQuery().taskId(taskId).singleResult();
        String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(t.getProcessInstanceId()).singleResult().getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        // 寻找流程实例当前任务的activeId
        Execution execution = runtimeService.createExecutionQuery().executionId(t.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();
        FlowNode currentNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId);
        FlowNode targetNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(sid);
        // 创建连接线
        List<SequenceFlow> newSequenceFlowList = new ArrayList<SequenceFlow>();
        SequenceFlow newSequenceFlow = new SequenceFlow();
        newSequenceFlow.setId("newFlow");
        newSequenceFlow.setSourceFlowElement(currentNode);
        newSequenceFlow.setTargetFlowElement(targetNode);
        newSequenceFlowList.add(newSequenceFlow);
        // 备份原有方向
        List<SequenceFlow> dataflows = currentNode.getOutgoingFlows();
        List<SequenceFlow> oriSequenceFlows = new ArrayList<SequenceFlow>();
        oriSequenceFlows.addAll(dataflows);
        // 清空原有方向
        currentNode.getOutgoingFlows().clear();
        // 设置新方向
        currentNode.setOutgoingFlows(newSequenceFlowList);
        // 完成当前任务
        taskService.addComment(taskId, t.getProcessInstanceId(), "comment", "跳转节点");
        taskService.complete(taskId);
        // 恢复原有方向
        currentNode.setOutgoingFlows(oriSequenceFlows);
    }


    /**
     * 驳回任务
     *
     * @param taskForm
     */
    @Override
    public void taskReject(FlowTaskForm taskForm) {
        // 取得所有历史任务按时间降序排序
        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(taskForm.getInstanceId())
                .orderByTaskCreateTime().desc().list();
        Integer size = 2;
        if (org.springframework.util.StringUtils.isEmpty(htiList) || htiList.size() < size) {
            new BizException("流程实例的首个节点不能驳回");
        }
        //所有历史任务根据  模型节点的唯一标识 分组 并在组内根据创建时间降序排序
        Map<String, List<HistoricTaskInstance>> htiGroup = htiList.stream()
                .collect(Collectors.groupingBy(
                        HistoricTaskInstance::getTaskDefinitionKey,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(HistoricTaskInstance::getCreateTime))
                                        .collect(Collectors.toList()))
                ));
        // list里第一条代表当前任务
        HistoricTaskInstance curTask = htiList.get(0);
        // 当前节点的executionId：历史任务实例相关联的执行ID
        String curExecutionId = curTask.getExecutionId();
        //根据 历史任务实例所属流程的定义ID 查询 流程模型，ID通常是BPMN模型的ID与其版本号的组合 例如exampleDemo:1:77003
        BpmnModel bpmnModel = repositoryService.getBpmnModel(curTask.getProcessDefinitionId());
        FlowElement currentElement = bpmnModel.getMainProcess().getFlowElement(curTask.getTaskDefinitionKey());
        String lastTaskDefinitionKey = findLastUserTaskDefinitionKey(currentElement, bpmnModel);
//        String lastTaskDefinitionKey = null;
//        if (currentElement != null && currentElement instanceof FlowNode) {
//            FlowNode currentNode = (FlowNode) currentElement;
//            // 获取入口连线
//            List<SequenceFlow> incomingFlows = currentNode.getIncomingFlows();
//            if (incomingFlows != null && !incomingFlows.isEmpty()) {
//                // 在大多数情况下，一个节点只有一个入口连线。
//                // 但是，如果存在多个连线，你需要根据你的需求来决定如何处理它们。
//                SequenceFlow incomingFlow = incomingFlows.get(0);
//                FlowNode previousNode = (FlowNode) incomingFlow.getSourceFlowElement();
//                //流程模型 当前节点的上一个节点的key
//                lastTaskDefinitionKey= previousNode.getId();
//
//            }
//        }
        List<HistoricTaskInstance> lastTaskResult = htiGroup.get(lastTaskDefinitionKey);
        //上一个任务
        HistoricTaskInstance lastTask =lastTaskResult.get(lastTaskResult.size() - 1);

        // 上一个任务的taskId
        String lastTaskId = lastTask.getId();
        if (null == lastTaskId) {
            throw new BizException("LAST TASK IS NULL");
        }
        String lastActivityId = null;
        List<HistoricActivityInstance> haiFinishedList = historyService.createHistoricActivityInstanceQuery()
                .executionId(lastTask.getExecutionId()).finished().list();
        for (HistoricActivityInstance hai : haiFinishedList) {
            if (lastTaskId.equals(hai.getTaskId())) {
                // 得到ActivityId，只有HistoricActivityInstance对象里才有此方法
                lastActivityId = hai.getActivityId();
                break;
            }
        }
        // 得到上个节点的信息
        FlowNode lastFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(lastActivityId);
        // 取得当前节点的信息
        Execution execution = runtimeService.createExecutionQuery().executionId(curExecutionId).singleResult();
        String curActivityId = execution.getActivityId();
        FlowNode curFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(curActivityId);

        // 记录当前节点的原活动方向
        List<SequenceFlow> oriSequenceFlows = new ArrayList<>();
        oriSequenceFlows.addAll(curFlowNode.getOutgoingFlows());
        // 清理活动方向
        curFlowNode.getOutgoingFlows().clear();
        // 建立新方向
        List<SequenceFlow> newSequenceFlowList = new ArrayList<>();
        SequenceFlow newSequenceFlow = new SequenceFlow();
        newSequenceFlow.setId("newFlow");
        newSequenceFlow.setSourceFlowElement(curFlowNode);
        newSequenceFlow.setTargetFlowElement(lastFlowNode);
        newSequenceFlowList.add(newSequenceFlow);
        curFlowNode.setOutgoingFlows(newSequenceFlowList);
        // 完成任务
        taskService.addComment(taskForm.getTaskId(),taskForm.getInstanceId(),FlowComment.REJECT.getType(),"【驳回】"+taskForm.getComment());
        taskService.complete(taskForm.getTaskId());
        // 恢复原方向
        curFlowNode.setOutgoingFlows(oriSequenceFlows);
        // 设置执行人
        Task nextTask = taskService.createTaskQuery().processInstanceId(taskForm.getInstanceId()).singleResult();
        if (nextTask != null) {
            taskService.setAssignee(nextTask.getId(), lastTask.getAssignee());
        }
        log.info("驳回成功");
    }


    // 递归查找上一个用户任务节点的定义ID
    private String findLastUserTaskDefinitionKey(FlowElement currentElement, BpmnModel bpmnModel) {
        if (currentElement != null && currentElement instanceof FlowNode) {
            FlowNode currentNode = (FlowNode) currentElement;
            List<SequenceFlow> incomingFlows = currentNode.getIncomingFlows();
            if (incomingFlows != null && !incomingFlows.isEmpty()) {
                for (SequenceFlow incomingFlow : incomingFlows) {
                    FlowElement sourceFlowElement = incomingFlow.getSourceFlowElement();
                    if (sourceFlowElement instanceof FlowNode) {
                        FlowNode sourceNode = (FlowNode) sourceFlowElement;
                        if (sourceNode instanceof UserTask) {
                            return sourceNode.getId(); // 找到了上一个用户任务节点的定义ID
                        } else {
                            // 继续向上查找
                            return findLastUserTaskDefinitionKey(sourceNode, bpmnModel);
                        }
                    }
                }
            }
        }
        return null; // 没有找到用户任务节点
    }

    /**
     * 获取流程过程图
     *
     * @param processId
     * @return
     */
    @Override
    public InputStream diagram(String processId) {
        BpmnModel bpmnModel = null;
        boolean showHistory=true;  //是否显示流程图历史节点渲染 true:显示历史节点,使用procInstId。 false:不显示历史节点,直接使用procDefId

        List<String> highLightedActivitiIdList = new ArrayList<>();
        List<String> runningActivitiIdList = new ArrayList<>();
        List<String> highLightedFlowIds = new ArrayList<>();

        // 通过流程实例ID获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyService
                .createHistoricProcessInstanceQuery()
                .processInstanceId(processId)
                .singleResult();
        String processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        if (showHistory && StringUtils.isNotEmpty(processId)) {
            if (null != historicProcessInstance) {
                // 获取流程定义Model对象
                bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                //如果需要展示历史节点记录，则需要查询出对应的历史节点信息
                if (showHistory) {
                    // 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
                    List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                            .processInstanceId(processId)
                            .orderByHistoricActivityInstanceId()
                            .asc().list();

                    // 将已经执行的节点ID放入高亮显示节点集合
                    for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceList) {
                        highLightedActivitiIdList.add(historicActivityInstance.getActivityId());
                    }

                    // 通过流程实例ID获取流程中正在执行的节点
                    List<Execution> runningActivityInstanceList = runtimeService.createExecutionQuery().
                            processInstanceId(processId).list();
                    for (Execution execution : runningActivityInstanceList) {
                        if (StringUtils.isNotEmpty(execution.getActivityId())) {
                            runningActivitiIdList.add(execution.getActivityId());
                        }
                    }

                    // 获取已流经的流程线，需要高亮显示高亮流程已发生流转的线id集合
                    highLightedFlowIds.addAll(ActivitiUtil.getHighLightedFlows(bpmnModel, historicActivityInstanceList));
                }
            }
        } else if (!showHistory && StringUtils.isNotEmpty(processDefinitionId)) {
            bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        }
        // 定义流程画布生成器
        CustomProcessDiagramGenerator processDiagramGenerator = new CustomProcessDiagramGenerator();
       // 使用默认配置获得流程图表生成器，并生成追踪图片字符流
        InputStream  in = processDiagramGenerator.generateDiagramCustom(bpmnModel, "png",
                highLightedActivitiIdList, runningActivitiIdList, highLightedFlowIds,
                "宋体", "黑体", "黑体",
                null, 2.0);
        return in;
    }


    /**
     * 我发起的流程
     * @param queryParams  请求参数
     * @return
     */
    @Override
    public IPage<TaskInfoVO> myProcess(FlowPageQuery queryParams) {
        Page<TaskInfoVO> page = new Page<>();
        Long userId = SecurityUtils.getUserId();
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .startedBy(userId.toString())
                .orderByProcessInstanceStartTime()
                .desc();
        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery.listPage(queryParams.getPageSize() * (queryParams.getPageNum() - 1), queryParams.getPageSize());
        page.setTotal(historicProcessInstanceQuery.count());
        List<TaskInfoVO> flowList = new ArrayList<>();
        for (HistoricProcessInstance hisIns : historicProcessInstances) {
            TaskInfoVO taskInfo = new TaskInfoVO();
            taskInfo.setCreateTime(hisIns.getStartTime());
            taskInfo.setEndTime(hisIns.getEndTime());
            taskInfo.setProcessInstanceId(hisIns.getId());

            // 计算耗时
            if (Objects.nonNull(hisIns.getEndTime())) {
                long time = hisIns.getEndTime().getTime() - hisIns.getStartTime().getTime();
                taskInfo.setDuration(DateUtil.getDate(time));
            } else {
                long time = System.currentTimeMillis() - hisIns.getStartTime().getTime();
                taskInfo.setDuration(DateUtil.getDate(time));
            }
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(hisIns.getProcessDefinitionId())
                    .singleResult();
            taskInfo.setDeployId(pd.getDeploymentId());
            taskInfo.setProcDefName(pd.getName());
            taskInfo.setCategory(pd.getCategory());
            // 当前所处流程
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisIns.getId()).list();
            if (CollectionUtils.isNotEmpty(taskList)) {
                taskInfo.setTaskId(taskList.get(0).getId());
                taskInfo.setTaskName(taskList.get(0).getName());
                if (StringUtils.isNotBlank(taskList.get(0).getAssignee())) {
                    // 当前任务节点办理人信息
                    SysAdmin sysUser = sysAdminService.selectUserById(Long.parseLong(taskList.get(0).getAssignee()));
                    if (Objects.nonNull(sysUser)) {
                        taskInfo.setAssigneeId(sysUser.getId());
                        taskInfo.setAssigneeName(sysUser.getNickname());
                    }
                }
            } else {
                List<HistoricTaskInstance> historicTaskInstance = historyService.createHistoricTaskInstanceQuery().processInstanceId(hisIns.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
                taskInfo.setTaskId(historicTaskInstance.get(0).getId());
                taskInfo.setTaskName(historicTaskInstance.get(0).getName());
                if (StringUtils.isNotBlank(historicTaskInstance.get(0).getAssignee())) {
                    // 当前任务节点办理人信息
                    SysAdmin sysUser = sysAdminService.selectUserById(Long.parseLong(historicTaskInstance.get(0).getAssignee()));
                    if (Objects.nonNull(sysUser)) {
                        taskInfo.setAssigneeId(sysUser.getId());
                        taskInfo.setAssigneeName(sysUser.getNickname());
                    }
                }
            }
            flowList.add(taskInfo);
        }


        return null;
    }


    /**
     * 取消申请
     * 目前实现方式: 直接将当前流程实例删除
     * @param form
     */
/*
    @Override
    public void stopProcess(FlowTaskForm form) {
        List<Task> task = taskService.createTaskQuery().processInstanceId(form.getInstanceId()).list();
        if (CollectionUtils.isEmpty(task)) {
            throw new BizException("流程未启动或已执行完成，取消申请失败");
        }
        // 获取当前流程实例
//        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(form.getInstanceId())
//                .singleResult();
        if (form.getInstanceId() != null) {
            runtimeService.deleteProcessInstance(form.getInstanceId(), "删除");
        }
        // 删除历史数据
        HistoricProcessInstance history = historyService.createHistoricProcessInstanceQuery().processInstanceId(form.getInstanceId()).singleResult();
        if (history != null){
            historyService.deleteHistoricProcessInstance(history.getId());
        }
    }
*/


    /**
     * 撤销:强制结束一个流程
     * @param taskId
     */
    @Override
    public void forceEnd(String taskId) {
        Task t = taskService.createTaskQuery().taskId(taskId).singleResult();
        String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(t.getProcessInstanceId()).singleResult().getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        // 寻找流程实例当前任务的activeId
        Execution execution = runtimeService.createExecutionQuery().executionId(t.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();
        FlowNode currentNode = (FlowNode)bpmnModel.getMainProcess().getFlowElement(activityId);
        // 创建结束节点和连接线
        EndEvent end = new EndEvent();
        end.setName("强制结束");
        end.setId("forceEnd");
        List<SequenceFlow> newSequenceFlowList = new ArrayList<SequenceFlow>();
        SequenceFlow newSequenceFlow = new SequenceFlow();
        newSequenceFlow.setId("newFlow");
        newSequenceFlow.setSourceFlowElement(currentNode);
        newSequenceFlow.setTargetFlowElement(end);
        newSequenceFlowList.add(newSequenceFlow);
        // 备份原有方向
        List<SequenceFlow> dataflows = currentNode.getOutgoingFlows();
        List<SequenceFlow> oriSequenceFlows = new ArrayList<SequenceFlow>();
        oriSequenceFlows.addAll(dataflows);
        // 清空原有方向
        currentNode.getOutgoingFlows().clear();
        // 设置新方向
        currentNode.setOutgoingFlows(newSequenceFlowList);
        // 完成当前任务
        taskService.addComment(taskId, t.getProcessInstanceId(), "comment", "撤销流程");
        taskService.complete(taskId);
        // 恢复原有方向
        currentNode.setOutgoingFlows(oriSequenceFlows);
    }
}
