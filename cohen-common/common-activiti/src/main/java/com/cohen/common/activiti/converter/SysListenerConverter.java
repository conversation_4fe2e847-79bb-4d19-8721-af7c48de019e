package com.cohen.common.activiti.converter;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.entity.SysListener;
import com.cohen.common.activiti.pojo.form.SysListenerForm;
import com.cohen.common.activiti.pojo.po.SysListenerPO;
import com.cohen.common.activiti.pojo.vo.SysListenerVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;


/**
 * 流程监听转换类
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Mapper(componentModel = "spring")
public interface SysListenerConverter {

    SysListenerVO po2Vo(SysListenerPO po);
    Page<SysListenerVO> po2Vo(Page<SysListenerPO> po);
    SysListenerVO entity2Vo(SysListener entity);

    @InheritInverseConfiguration(name = "entity2Form")
    SysListener form2Entity(SysListenerForm form);
}
