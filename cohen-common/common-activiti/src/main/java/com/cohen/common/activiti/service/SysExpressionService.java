
package com.cohen.common.activiti.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.common.activiti.pojo.entity.SysExpression;
import com.cohen.common.activiti.pojo.form.SysExpressionForm;
import com.cohen.common.activiti.pojo.query.SysExpressionPageQuery;
import com.cohen.common.activiti.pojo.vo.SysExpressionVO;


/**
 * 流程达式Service接口
 *
 * <AUTHOR>
 * @date 2024-05-16
 */

public interface SysExpressionService extends IService<SysExpression> {

    /**
     * 分頁列表
     * @return
     */
    IPage<SysExpressionVO> listSysExpressionPages(SysExpressionPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    SysExpressionVO getSysExpressionData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveSysExpression(SysExpressionForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateSysExpression(Long id, SysExpressionForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteSysExpression(String idsStr);
}


