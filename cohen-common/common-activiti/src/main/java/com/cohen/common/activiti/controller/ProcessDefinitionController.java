package com.cohen.common.activiti.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.activiti.pojo.form.FlowSaveXmlForm;
import com.cohen.common.activiti.pojo.query.ProcessDefinitionPageQuery;
import com.cohen.common.activiti.pojo.vo.ProcessDefinitionVO;
import com.cohen.common.activiti.service.ProcessDefinitionService;
import com.cohen.common.activiti.service.impl.ProcessDefinitionServiceImpl;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 * @create 2024-05-10
 */
@Slf4j
@Api(tags = "流程定义接口")
@RestController
@RequestMapping("/api/v1/process/definition")
@RequiredArgsConstructor
public class ProcessDefinitionController {

    private final ProcessDefinitionService processDefinitionService;

    private final RepositoryService repositoryService;


    @ApiOperation(value = "查询流程定义分页列表")
    @GetMapping("/pages")
    private PageResult lisProcessDefinitionPages(ProcessDefinitionPageQuery queryParams){
        IPage<ProcessDefinitionVO> result = processDefinitionService.lisProcessDefinitionPages(queryParams);
        return PageResult.success(result);
    }


    @ApiOperation(value = "上传并部署流程定义")
    @PostMapping("/uploadWorkflow")
    public Result uploadWorkflow(@RequestParam MultipartFile file){
        try {
            String filename = file.getOriginalFilename();
            InputStream is = file.getInputStream();
            if (filename.endsWith("zip")) {
                repositoryService.createDeployment().addZipInputStream(new ZipInputStream(is)).deploy();
            } else if (filename.endsWith("bpmn") || filename.endsWith("xml")) {
                repositoryService.createDeployment().addInputStream(filename, is).deploy();
            } else {
                return Result.failed("文件格式错误");
            }
        }catch (Exception e) {
            e.printStackTrace();
            return Result.failed("上传失败");
        }
        return Result.success();
    }

    @ApiOperation(value = "保存流程设计器内的xml文件")
    @PostMapping("/addDeployment")
    public Result addDeployment(@RequestBody FlowSaveXmlForm form){
        InputStream in = null;
        try {
            in = new ByteArrayInputStream(form.getXml().getBytes(StandardCharsets.UTF_8));
            processDefinitionService.importFile(form.getName(), form.getCategory(), in);
        } catch (Exception e) {
            log.error("保存失败:", e);
            return Result.failed(e.getMessage());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("关闭输入流出错", e);
            }
        }
        return Result.success("保存成功");
    }

    @ApiOperation(value = "读取xml文件")
    @GetMapping("/readXml/{deployId}")
    public Result readXml(@ApiParam(value = "流程定义id") @PathVariable(value = "deployId") String deployId) {
        try {
            String result = processDefinitionService.readXml(deployId);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("加载xml文件异常");
        }
    }

    @ApiOperation(value = "读取图片文件")
    @GetMapping("/readImage/{deployId}")
    public void readImage(@ApiParam(value = "流程定义id") @PathVariable(value = "deployId") String deployId, HttpServletResponse response) {
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(processDefinitionService.readImage(deployId));
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @ApiOperation(value = "获取流程定义XML")
    @GetMapping(value = "/getDefinitionXML")
    public void getProcessDefineXML(HttpServletResponse response,
                                    @RequestParam("pdid") String pdid,
                                    @RequestParam("resource") String resource) throws IOException {
        InputStream is = repositoryService.getResourceAsStream(pdid, resource);
        ServletOutputStream output = response.getOutputStream();
        IOUtils.copy(is, output);
    }

    @ApiOperation(value = "发起流程")
    @PostMapping("/start/{procDefId}")
    public Result start(@ApiParam(value = "流程定义key") @PathVariable(value = "procDefKey") String procDefKey,
                        @ApiParam(value = "业务id") @RequestParam(required = false) Long businessId,
                        @ApiParam(value = "变量集合,json对象") @RequestBody Map<String, Object> variables) {
        processDefinitionService.startProcessInstanceById(procDefKey,businessId,variables);
        return Result.success();
    }

    @ApiOperation(value = "激活或挂起流程定义")
    @PutMapping(value = "/updateState")
    public Result updateState(@ApiParam(value = "1:激活,2:挂起", required = true) @RequestParam Integer state,
                                  @ApiParam(value = "流程部署ID", required = true) @RequestParam String deployId) {
        processDefinitionService.updateState(state, deployId);
        return Result.success();
    }

    @ApiOperation(value = "删除流程定义")
    @DeleteMapping(value = "/remove/{deploymentId}")
    public Result delDefinition(@PathVariable("deploymentId") String deploymentId) {
        processDefinitionService.deleteProcessDefinitionById(deploymentId);
        return Result.judge(true);
    }


}
