package com.cohen.common.activiti.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.entity.SysForm;
import com.cohen.common.activiti.pojo.form.SysFormForm;
import com.cohen.common.activiti.pojo.po.SysFormPO;
import com.cohen.common.activiti.pojo.vo.SysFormVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;


/**
 * 流程单转换类
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Mapper(componentModel = "spring")
public interface SysFormConverter {

    SysFormVO po2Vo(SysFormPO po);
    Page<SysFormVO> po2Vo(Page<SysFormPO> po);
    SysFormVO entity2Vo(SysForm entity);

    @InheritInverseConfiguration(name = "entity2Form")
    SysForm form2Entity(SysFormForm form);
}
