package com.cohen.common.activiti.converter;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.entity.SysExpression;
import com.cohen.common.activiti.pojo.form.SysExpressionForm;
import com.cohen.common.activiti.pojo.po.SysExpressionPO;
import com.cohen.common.activiti.pojo.vo.SysExpressionVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;


/**
 * 流程达式转换类
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Mapper(componentModel = "spring")
public interface SysExpressionConverter {

    SysExpressionVO po2Vo(SysExpressionPO po);
    Page<SysExpressionVO> po2Vo(Page<SysExpressionPO> po);
    SysExpressionVO entity2Vo(SysExpression entity);

    @InheritInverseConfiguration(name = "entity2Form")
    SysExpression form2Entity(SysExpressionForm form);
}
