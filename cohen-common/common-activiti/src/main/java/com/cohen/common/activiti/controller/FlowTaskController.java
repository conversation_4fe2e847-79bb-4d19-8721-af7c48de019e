package com.cohen.common.activiti.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.activiti.pojo.form.FlowTaskForm;
import com.cohen.common.activiti.pojo.query.TaskPageQuery;
import com.cohen.common.activiti.pojo.vo.TaskInfoVO;
import com.cohen.common.activiti.service.FlowTaskService;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-14
 */

@Slf4j
@Api(tags = "流程任务管理")
@RestController
@RequestMapping("/api/v1/task")
@RequiredArgsConstructor
public class FlowTaskController {

    private final FlowTaskService flowTaskService;


    @ApiOperation(value = "审批任务")
    @PostMapping(value = "/complete")
    public Result complete(@RequestBody FlowTaskForm flowTaskForm) {
        flowTaskService.complete(flowTaskForm);
        return Result.success();
    }

    @ApiOperation(value = "驳回任务")
    @PostMapping(value = "/reject")
    public Result taskReject(@RequestBody FlowTaskForm flowTaskForm) {
        flowTaskService.taskReject(flowTaskForm);
        return Result.success();
    }

    @ApiOperation(value = "查询历史任务分页列表")
    @GetMapping("/queryHistoryTask")
    public PageResult queryHistoryTask(TaskPageQuery queryParams) {
        IPage<TaskInfoVO> result = flowTaskService.queryHistoryTask(queryParams);
        return PageResult.success(result);
    }


    @ApiOperation(value = "获取流程变量")
    @GetMapping(value = "/processVariables/{taskId}")
    public Result processVariables(@ApiParam(value = "流程任务Id") @PathVariable(value = "taskId") String taskId) {
        Map<String, Object> result = flowTaskService.processVariables(taskId);
        return Result.success(result);
    }


    @ApiOperation(value = "获取我的代办任务列表")
    @GetMapping("/myList")
    public PageResult myList(TaskPageQuery queryParams) {
        IPage<TaskInfoVO> result = flowTaskService.myList(queryParams);
        return PageResult.success(result);
    }

    @ApiOperation(value = "获取我的已办任务")
    @GetMapping(value = "/finishedList")
    public PageResult finishedList(TaskPageQuery queryParams) {
        IPage<TaskInfoVO> result = flowTaskService.finishedList(queryParams);
        return PageResult.success(result);
    }

    @ApiOperation(value = "流程图进度追踪")
    @GetMapping("/traceProcess")
    public void traceProcess(String instanceId, HttpServletResponse response) throws IOException {
        flowTaskService.traceProcess(instanceId, response);
    }

    @ApiOperation(value = "退回任务")
    @PostMapping(value = "/return")
    public Result taskReturn(@RequestBody FlowTaskForm flowTaskVo) {
        flowTaskService.taskReturn(flowTaskVo);
        return Result.success();
    }

    @ApiOperation(value = "获取所有可回退的节点")
    @PostMapping(value = "/returnList")
    public Result findReturnTaskList(@RequestBody FlowTaskForm form) {
        return Result.success(flowTaskService.findReturnTaskList(form));
    }

    @ApiOperation(value = "认领/签收任务")
    @PostMapping(value = "/claim")
    public Result claim(@RequestBody FlowTaskForm form) {
        flowTaskService.claim(form);
        return Result.success();
    }

    @ApiOperation(value = "取消认领/签收任务")
    @PostMapping(value = "/unClaim")
    public Result unClaim(@RequestBody FlowTaskForm form) {
        flowTaskService.unClaim(form);
        return Result.success();
    }

    @ApiOperation(value = "委派任务")
    @PostMapping(value = "/delegateTask")
    public Result delegate(@RequestBody FlowTaskForm form) {
        flowTaskService.delegateTask(form);
        return Result.success();
    }

    @ApiOperation(value = "任务归还")
    @PostMapping(value = "/resolveTask")
    public Result resolveTask(@RequestBody FlowTaskForm form) {
        flowTaskService.resolveTask(form);
        return Result.success();
    }

    @ApiOperation(value = "转办任务")
    @PostMapping(value = "/assignTask")
    public Result assign(@RequestBody FlowTaskForm form) {
        flowTaskService.assignTask(form);
        return Result.success();
    }

    @ApiOperation("跳转到指定节点")
    @GetMapping(value = "/jump/{taskId}")
    public Result jump(@PathVariable String taskId, @RequestParam String sid) {
        flowTaskService.jump(taskId, sid);
        return Result.success();
    }

    @ApiOperation("撤销:强制结束一个流程")
    @GetMapping(value = "/forceEnd/{taskId}")
    public Result forceEnd(@PathVariable String taskId){
        flowTaskService.forceEnd(taskId);
        return Result.success();
    }

    @ApiOperation(value = "查询流程图进度")
    @GetMapping("/diagram/{processId}")
    public void genProcessDiagram(HttpServletResponse response,
                                  @PathVariable("processId") String processId) {
        InputStream inputStream = flowTaskService.diagram(processId);
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
