package com.cohen.common.activiti.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.Expression;
import org.springframework.stereotype.Component;

/**
 * 执行监听器
 * <p>
 * 执行监听器允许在执行过程中执行Java代码。
 * 执行监听器可以捕获事件的类型：
 * 流程实例启动，结束
 * 输出流捕获
 * 获取启动，结束
 * 路由开始，结束
 * 中间事件开始，结束
 * 触发开始事件，触发结束事件
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FlowExecutionListener implements ExecutionListener {
    /**
     * 流程设计器添加的参数
     */
    private Expression param;

    @Override
    public void notify(DelegateExecution execution) {
        log.info("执行监听器:{}", execution);
        String eventName = execution.getEventName();
        switch (eventName) {
            case EVENTNAME_START: //在流程实例启动时触发
                break;
            case EVENTNAME_END: //在流程实例结束时触发
                break;
            case EVENTNAME_TAKE: //流程连线被执行时触发
                break;
            default:
                break;
        }

    }
}
