package com.cohen.common.activiti.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.activiti.mapper.SysListenerMapper;
import com.cohen.common.activiti.pojo.entity.SysListener;
import com.cohen.common.activiti.pojo.form.SysListenerForm;
import com.cohen.common.activiti.pojo.po.SysListenerPO;
import com.cohen.common.activiti.pojo.query.SysListenerPageQuery;
import com.cohen.common.activiti.pojo.vo.SysListenerVO;
import com.cohen.common.activiti.service.SysListenerService;
import com.cohen.common.activiti.converter.SysListenerConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;



/**
 * 流程监听Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
@RequiredArgsConstructor
public class SysListenerServiceImpl extends ServiceImpl<SysListenerMapper, SysListener>implements SysListenerService {

    private final SysListenerConverter SysListenerConverter;


    @Override
    public IPage<SysListenerVO> listSysListenerPages(SysListenerPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<SysListenerPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<SysListenerPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<SysListenerVO> result = SysListenerConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public SysListenerVO getSysListenerData(Long id) {
        SysListener sd = this.getById(id);
        SysListenerVO SysListenerVO = SysListenerConverter.entity2Vo(sd);
        return SysListenerVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveSysListener(SysListenerForm form) {
        SysListener SysListener = SysListenerConverter.form2Entity(form);
        boolean result = this.save(SysListener);
        return result;
    }

    @Override
    public boolean updateSysListener(Long id, SysListenerForm form) {
        SysListener ahDoctor = SysListenerConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteSysListener(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
