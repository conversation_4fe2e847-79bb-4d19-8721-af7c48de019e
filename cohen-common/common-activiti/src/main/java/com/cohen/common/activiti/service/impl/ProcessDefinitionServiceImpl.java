package com.cohen.common.activiti.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.common.activiti.pojo.query.ProcessDefinitionPageQuery;
import com.cohen.common.activiti.pojo.vo.ProcessDefinitionVO;
import com.cohen.common.activiti.service.ProcessDefinitionService;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.web.exception.BizException;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.activiti.image.impl.DefaultProcessDiagramGenerator;
import org.apache.commons.io.IOUtils;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2024-05-10
 */
@Service
@RequiredArgsConstructor
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {

    private final RepositoryService repositoryService;
    private final IdentityService identityService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;

    private static final String BPMN_FILE_SUFFIX = ".bpmn";

    @Override
    public IPage<ProcessDefinitionVO> lisProcessDefinitionPages(ProcessDefinitionPageQuery queryParams) {
        Page<ProcessDefinitionVO> result = new Page<>();
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery().orderByProcessDefinitionId().orderByProcessDefinitionVersion().desc();
        if (StringUtils.isNotBlank(queryParams.getName())) {
            processDefinitionQuery.processDefinitionNameLike("%" + queryParams.getName() + "%");
        }
        if (StringUtils.isNotBlank(queryParams.getKey())) {
            processDefinitionQuery.processDefinitionKeyLike("%" + queryParams.getKey() + "%");
        }
        if (StringUtils.isNoneEmpty(queryParams.getCategory())){
            processDefinitionQuery.processDefinitionCategoryLike("%"+ queryParams.getCategory()+"%");
        }
        List<ProcessDefinition> processDefinitions = processDefinitionQuery.listPage((queryParams.getPageNum() - 1) * queryParams.getPageSize(), queryParams.getPageSize());
        long count = processDefinitionQuery.count();
        result.setTotal(count);
        LinkedList<ProcessDefinitionVO> list = new LinkedList<>();
        processDefinitions.stream().forEach(processDefinition -> {
            ProcessDefinitionVO data = new ProcessDefinitionVO();
            data.setId(processDefinition.getId());
            data.setName(processDefinition.getName());
            data.setKey(processDefinition.getKey());
            data.setVersion(processDefinition.getVersion());
            data.setDeploymentId(processDefinition.getDeploymentId());
            data.setResourceName(processDefinition.getResourceName());
            //true表示挂起状态   false表示激活状态
            data.setSuspendState(processDefinition.isSuspended()?2:1);
            data.setCategory(processDefinition.getCategory());
            Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(processDefinition.getDeploymentId()).singleResult();
            data.setDeploymenTime(deployment.getDeploymentTime());
            list.add(data);
        });
        result.setRecords(list);
        return result;
    }

    /**
     * 激活或挂起流程定义
     *
     * @param state    状态
     * @param deployId 流程部署ID
     */
    @Override
    public void updateState(Integer state, String deployId) {
        ProcessDefinition procDef = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        //激活
        if (state == 1) {
            repositoryService.activateProcessDefinitionById(procDef.getId(), true, null);
        }
        //挂起
        if (state == 2) {
            // 当流程定义被挂起时，已经发起的该流程定义的流程实例不受影响（如果选择级联挂起则流程实例也会被挂起）。
            // 当流程定义被挂起时，无法发起新的该流程定义的流程实例。
            // 直观变化：act_re_procdef 的 SUSPENSION_STATE_ 为 2
            repositoryService.suspendProcessDefinitionById(procDef.getId(), true, null);
        }
    }


    /**
     * 导入流程文件
     *
     * 当每个key的流程第一次部署时，指定版本为1。对其后所有使用相同key的流程定义，
     * 部署时版本会在该key当前已部署的最高版本号基础上加1。key参数用于区分流程定义
     * @param name
     * @param category
     * @param in
     */
    @Override
    public void importFile(String name, String category, InputStream in) {
        Deployment deploy = repositoryService.createDeployment().addInputStream(name + BPMN_FILE_SUFFIX, in).name(name).category(category).deploy();
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), category);

    }

    /**
     * 读取xml
     * @param deployId
     * @return
     */
    @Override
    public String readXml(String deployId) throws IOException {
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        InputStream inputStream = repositoryService.getResourceAsStream(definition.getDeploymentId(), definition.getResourceName());
        String result = IOUtils.toString(inputStream, StandardCharsets.UTF_8.name());
        return result;
    }

    /**
     * 读取图片文件
     * @param deployId
     * @return
     */
    @Override
    public InputStream readImage(String deployId) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        //获得图片流
        DefaultProcessDiagramGenerator diagramGenerator = new DefaultProcessDiagramGenerator();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        //输出为图片
        InputStream inputStream = diagramGenerator.generateDiagram(bpmnModel,
                "png",
                "宋体",
                "宋体",
                "宋体",
                null,
                1.0);
        return inputStream;
    }

    /**
     * 删除流程定义
     * @param deploymentId
     */
    @Override
    public void deleteProcessDefinitionById(String deploymentId) {
        // true 允许级联删除 ,不设置会导致数据库外键关联异常
        repositoryService.deleteDeployment(deploymentId,true);
    }

    /**
     * 根据流程定义Key启动流程实例
     * @param procDefKey
     * @param variables
     */
    @Override
    public void startProcessInstanceById(String procDefKey,Long businessId, Map<String, Object> variables) {
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(procDefKey)
//                .latestVersion().singleResult();
//        if (Objects.nonNull(processDefinition) && processDefinition.isSuspended()) {
//            throw new BizException("流程已被挂起,请先激活流程");
//        }
//        // 设置流程发起人Id到流程中
//        identityService.setAuthenticatedUserId(String.valueOf(SecurityUtils.getUserId()));
//        runtimeService.startProcessInstanceByKey(procDefKey,String.valueOf(businessId),variables);
//        //自动完成第一个任务
//        Task task = taskService.createTaskQuery().processDefinitionKey(procDefKey)
//                .processInstanceBusinessKey(String.valueOf(businessId))
//                .singleResult();
//        taskService.complete(task.getId());
    }
}
