package com.cohen.operlog.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 * @Date 2023-10-09 15:00
 * @Version 1.0.0
 */
public enum UserStatus
{
    OK("0", "正常"), DISABLE("1", "停用"), DELETED("2", "删除"),NOUSER("-2", "未生成");

    private final String code;
    private final String info;

    UserStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
