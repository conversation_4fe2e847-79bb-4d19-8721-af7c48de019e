package com.cohen.common.security.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cohen.common.constant.SecurityConstants;
import com.cohen.common.result.Result;
import com.cohen.common.result.ResultCode;
import com.cohen.common.security.util.RequestUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author：shaxiaosong
 * @name：SpringSecurityListener
 * @Date：2023-10-12 16:10
 */
@Component
@RequiredArgsConstructor
public class SpringSecurityListener {

    private final Logger log= LoggerFactory.getLogger(this.getClass());
    private final RedisTemplate redisTemplate;

    /**
     * 认证成功
     * @param authenticationSuccessEvent
     */
    @EventListener
    public void successListener(AuthenticationSuccessEvent authenticationSuccessEvent){
        //获取认证信息,即authenticationManager.authenticate()返回的Authentication
        //Authentication authentication=authenticationSuccessEvent.getAuthentication();
        //逻辑代码

        try{
            //获取认证用户信息,认证失败无论用户名不存在/密码错误都返回输入的用户名(String)
            String payload = RequestUtils.getJwtPayload();
            System.out.println("token success: {}" + payload);
            if (StrUtil.isNotBlank(payload)) {
                JSONObject entries = JSONUtil.parseObj(payload);
                if (entries != null) {
                    String jti = entries.getStr("jti"); // JWT唯一标识
                    Long expireTime = entries.getLong("exp"); // JWT过期时间戳(单位：秒)
                    //黑名单校验
                    if(redisTemplate.hasKey(SecurityConstants.BLACKLIST_TOKEN_PREFIX + jti)){
                        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
                        response.setContentType("application/json");
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        ObjectMapper mapper = new ObjectMapper();
                        mapper.writeValue(response.getOutputStream(), Result.failed(ResultCode.INVALID_TOKEN));
                    }
                }
            }
        }catch (Exception e){
            System.out.println("JWT token fail error: {}" + e.getMessage());
        }
    }

    /**
     * 认证失败
     * @param authenticationFailureBadCredentialsEvent
     */
    @EventListener
    public void failListener(AuthenticationFailureBadCredentialsEvent authenticationFailureBadCredentialsEvent){
        //获取认证信息,即authenticationManager.authenticate()返回的Authentication
        Authentication authentication=authenticationFailureBadCredentialsEvent.getAuthentication();

        //获取认证失败异常,认证失败的用户名不存在/密码错误BadCredentialsException
        AuthenticationException authenticationException=authenticationFailureBadCredentialsEvent.getException();
        //log.info("login fail exception: {}",authenticationException.toString());

        //获取认证用户信息,认证失败无论用户名不存在/密码错误都返回输入的用户名(String)
        String principal=authentication.getPrincipal().toString();
        //log.info("login fail principal: {}",principal);
    }
}

