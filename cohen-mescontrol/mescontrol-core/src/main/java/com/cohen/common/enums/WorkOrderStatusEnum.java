package com.cohen.common.enums;

import com.cohen.common.base.IBaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024-05-14
 */
public enum WorkOrderStatusEnum implements IBaseEnum<Integer> {

    PENDING_APPROVAL(0, "待审批"),
    PENDING_PROCESSING(1, "待处理"),
    REJECTED(2, "已驳回"),
    PROCESSED(3, "已处理"),
    CANCELED(4, "已取消");

    @Getter
    private Integer value;

    @Getter
    private String label;

    WorkOrderStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

}
