<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInventoryStatisticsMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInventoryStatisticsPO">
        select * from dev_consumable_inventory_statistics
        <where>
                        <if test="queryParams.consumableId != null "> and consumable_id = #{queryParams.consumableId}</if>
                        <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
                        <if test="queryParams.num != null "> and num = #{queryParams.num}</if>
                        <if test="queryParams.warehouseId != null "> and warehouse_id = #{queryParams.warehouseId}</if>
                        <if test="queryParams.warehouseName != null "> and warehouse_name like concat('%', #{queryParams.warehouseName}, '%')</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>



    <select id="inventoryPages" resultType="com.cohen.control.dev.pojo.vo.DevConsumableInventoryS1VO">
        select dcis.consumable_id,dcis.consumable_name,SUM(dcis.num) as num ,dci.model,dci.unit,dci.consumable_type_name,
        CASE
        WHEN  SUM(dcis.num) &lt;= dci.warning_inventory THEN 2
        ELSE 1
        END AS warnType
        from dev_consumable_inventory_statistics dcis
        LEFT JOIN dev_consumable_info dci on dci.id =  dcis.consumable_id
        <where>

            <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and dcis.consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
            <if test="queryParams.consumableTypeName != null  and queryParams.consumableTypeName != ''"> and dci.consumable_type_name like concat('%', #{queryParams.consumableTypeName}, '%')</if>
        </where>
        GROUP BY dcis.consumable_id
        <if test="queryParams.warnType != null ">
            HAVING
            warnType = #{queryParams.warnType};
        </if>


    </select>



</mapper>
