<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceExceptionRecordMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceExceptionRecordPO">
        select * from dev_device_exception_record
        <where>
            <if test="queryParams.deviceNo != null and queryParams.deviceNo != '' "> and device_no like concat('%', #{queryParams.deviceNo}, '%')</if>
            <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
