<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInboundMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInboundPO">
        select * from dev_consumable_inbound
        <where>
            <if test="queryParams.no != null  and queryParams.no != ''"> and no = #{queryParams.no}</if>
            <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and batch_number = #{queryParams.batchNumber}</if>
            <if test="queryParams.warehouseId != null "> and warehouse_id = #{queryParams.warehouseId}</if>
            <if test="queryParams.warehouse != null  and queryParams.warehouse != ''"> and warehouse = #{queryParams.warehouse}</if>
            <if test="queryParams.contactPerson != null  and queryParams.contactPerson != ''"> and contact_person = #{queryParams.contactPerson}</if>
            <if test="queryParams.contactPhone != null  and queryParams.contactPhone != ''"> and contact_phone = #{queryParams.contactPhone}</if>
            <if test="queryParams.startTime != null  and queryParams.startTime != ''">
                and date(warehouse_date) &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null  and queryParams.endTime != ''">
                and date(warehouse_date) &lt;= #{queryParams.endTime}
            </if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
