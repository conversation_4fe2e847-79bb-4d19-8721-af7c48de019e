<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceRecordMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceRecordPO">
        select ddr.*,dt.name as deviceTypeName from dev_device_record ddr
        left join dev_device_type dt on dt.id = ddr.device_type_id
        <where>
            <if test="queryParams.deviceNo != null  and queryParams.deviceNo != ''"> and ddr.device_no = #{queryParams.deviceNo}</if>
            <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and ddr.device_name like concat('%', #{queryParams.deviceName}, '%')</if>
            <if test="queryParams.deviceTypeId != null "> and ddr.device_type_id = #{queryParams.deviceTypeId}</if>
            <if test="queryParams.brand != null  and queryParams.brand != ''"> and ddr.brand = #{queryParams.brand}</if>
            <if test="queryParams.manufacturer != null  and queryParams.manufacturer != ''"> and ddr.manufacturer = #{queryParams.manufacturer}</if>
            <if test="queryParams.operatorType != null  and queryParams.operatorType != ''"> and ddr.operator_type = #{queryParams.operatorType}</if>
            <if test="queryParams.operator != null  and queryParams.operator != ''"> and ddr.operator = #{queryParams.operator}</if>
            <if test="queryParams.operatorTime != null "> and ddr.operator_time = #{queryParams.operatorTime}</if>
            <if test="queryParams.operatorResult != null  and queryParams.operatorResult != ''"> and ddr.operator_result = #{queryParams.operatorResult}</if>
            <if test="queryParams.taskNo != null  and queryParams.taskNo != ''"> and ddr.task_no = #{queryParams.taskNo}</if>
            <if test="queryParams.planNo != null  and queryParams.planNo != ''"> and ddr.plan_no = #{queryParams.planNo}</if>
            <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''"> and ddr.sample_no = #{queryParams.sampleNo}</if>
            <if test="queryParams.startTime != null  and queryParams.startTime != ''">
                and date(ddr.create_time) &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null  and queryParams.endTime != ''">
                and date(ddr.create_time) &gt;= #{queryParams.endTime}
            </if>

            and ddr.del_flag = 0
        </where>
        ORDER BY ddr.create_time DESC
    </select>

</mapper>
