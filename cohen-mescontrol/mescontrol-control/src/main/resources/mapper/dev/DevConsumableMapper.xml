<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumablePO">
        select dc.*,dci.consumable_type_name,dci.model,dci.manufacturer,dci.unit,dci.brand from dev_consumable dc
        JOIN dev_consumable_info dci on dc.consumable_id = dci.id
        <where>
                        <if test="queryParams.inboundNo != null  and queryParams.inboundNo != ''"> and dc.inbound_no = #{queryParams.inboundNo}</if>
                        <if test="queryParams.code != null  and queryParams.code != ''"> and dc.code = #{queryParams.code}</if>
                        <if test="queryParams.consumableId != null "> and dc.consumable_id = #{queryParams.consumableId}</if>
                        <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and dc.consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
                        <if test="queryParams.warehouseId != null "> and dc.warehouse_id = #{queryParams.warehouseId}</if>
                        <if test="queryParams.warehouseName != null  and queryParams.warehouseName != ''"> and dc.warehouse_name like concat('%', #{queryParams.warehouseName}, '%')</if>
                        <if test="queryParams.manufactureDate != null "> and dc.manufacture_date = #{queryParams.manufactureDate}</if>
                        <if test="queryParams.expirationDate != null "> and dc.expiration_date = #{queryParams.expirationDate}</if>
                        <if test="queryParams.price != null "> and dc.price = #{queryParams.price}</if>
                        <if test="queryParams.status != null "> and dc.status = #{queryParams.status}</if>
                        <if test="queryParams.consumableCategoryId != null "> and dc.consumable_category_id = #{queryParams.consumableCategoryId}</if>
            and dc.del_flag = 0
        </where>
        ORDER BY dc.create_time DESC
    </select>


    <select id="inventeryList" resultType="com.cohen.control.dev.pojo.vo.DevConsumableInventoryVO">
        SELECT
        c.warehouse_name as name,
        c.warehouse_id as id,
        w.region,
        w.address,
        w.manager,
        w.manager_contact,
        COUNT(c.id) AS count,
        CASE
        WHEN COUNT(c.id) &lt; i.warning_inventory THEN '库存预警'
        ELSE '库存充足'
        END AS warnType
        FROM
        dev_consumable c
        LEFT JOIN
        dev_warehouse w ON c.warehouse_id = w.id
        LEFT JOIN
        dev_consumable_info i ON c.consumable_id = i.id
        <where>
            c.consumable_id = #{consumableId}
            <if test="warehouseName != null  and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="region != null  and region != ''"> and w.region like concat('%', #{region}, '%')</if>
            <if test="manager != null  and manager != ''"> and w.manager like concat('%', #{manager}, '%')</if>
        </where>
        GROUP BY
            c.warehouse_name,
            c.warehouse_id,
            w.region,
            w.address,
            w.manager,
            w.manager_contact
    </select>


</mapper>
