<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDevicePO">
        select d.*,di.param_config from dev_device d
        left join dev_device_info di on di.id= d.device_info_id
        <where>
                        <if test="queryParams.code != null  and queryParams.code != ''"> and d.code = #{queryParams.code}</if>
                        <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and d.batch_number = #{queryParams.batchNumber}</if>
                        <if test="queryParams.barCode != null  and queryParams.barCode != ''"> and d.bar_code = #{queryParams.barCode}</if>
                        <if test="queryParams.deviceTypeId != null  and queryParams.deviceTypeId != ''"> and d.device_type_id = #{queryParams.deviceTypeId}</if>
                        <if test="queryParams.price != null "> and d.price = #{queryParams.price}</if>
                        <if test="queryParams.unit != null  and queryParams.unit != ''"> and d.unit = #{queryParams.unit}</if>
                        <if test="queryParams.name != null "> and d.name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.model != null  and queryParams.model != ''"> and d.model = #{queryParams.model}</if>
                        <if test="queryParams.version != null  and queryParams.version != ''"> and d.version = #{queryParams.version}</if>
                        <if test="queryParams.brand != null  and queryParams.brand != ''"> and d.brand = #{queryParams.brand}</if>
                        <if test="queryParams.Manufacturer != null  and queryParams.Manufacturer != ''"> and d.Manufacturer like  concat('%',#{queryParams.Manufacturer},'%') </if>
                        <if test="queryParams.productionDate != null "> and d.production_date = #{queryParams.productionDate}</if>
                        <if test="queryParams.storageTime != null "> and d.storage_time = #{queryParams.storageTime}</if>
                        <if test="queryParams.images != null  and queryParams.images != ''"> and d.images = #{queryParams.images}</if>
                        <if test="queryParams.ownerId != null "> and d.owner_id = #{queryParams.ownerId}</if>
                        <if test="queryParams.ownerRole != null  and queryParams.ownerRole != ''"> and d.owner_role = #{queryParams.ownerRole}</if>
                        <if test="queryParams.lng != null  and queryParams.lng != ''"> and d.lng = #{queryParams.lng}</if>
                        <if test="queryParams.lat != null  and queryParams.lat != ''"> and d.lat = #{queryParams.lat}</if>
                        <if test="queryParams.region != null  and queryParams.region != ''"> and d.region = #{queryParams.region}</if>
                        <if test="queryParams.cause != null  and queryParams.cause != ''"> and d.cause = #{queryParams.cause}</if>
                        <if test="queryParams.installationStatus != null "> and d.Installation_status = #{queryParams.installationStatus}</if>
                        <if test="queryParams.status != null "> and d.status = #{queryParams.status}</if>
                        <if test="queryParams.storageLocation != null  and queryParams.storageLocation != ''"> and d.storage_location = #{queryParams.storageLocation}</if>
                        <if test="queryParams.macLocation != null  and queryParams.macLocation != ''"> and d.mac_location = #{queryParams.macLocation}</if>
                        <if test="queryParams.port != null  and queryParams.port != ''"> and d.port = #{queryParams.port}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and d.extra = #{queryParams.extra}</if>
            and d.del_flag = 0
        </where>
        ORDER BY d.create_time DESC
    </select>

</mapper>
