<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevMaintenanceMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevMaintenancePO">
        select * from dev_maintenance
        <where>
                        <if test="queryParams.nickname != null  and queryParams.nickname != ''"> and nickname like concat('%', #{queryParams.nickname}, '%')</if>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.phone != null  and queryParams.phone != ''"> and phone like concat('%',#{queryParams.phone},'%') </if>
                        <if test="queryParams.inboundTime != null "> and inbound_time = #{queryParams.inboundTime}</if>
                        <if test="queryParams.idCard != null  and queryParams.idCard != ''"> and id_card = #{queryParams.idCard}</if>
                        <if test="queryParams.idCardJust != null  and queryParams.idCardJust != ''"> and id_card_just = #{queryParams.idCardJust}</if>
                        <if test="queryParams.idCardBack != null  and queryParams.idCardBack != ''"> and id_card_back = #{queryParams.idCardBack}</if>
                        <if test="queryParams.regionId != null  and queryParams.regionId != ''"> and region_id like concat(#{queryParams.regionId},'%')</if>
                        <if test="queryParams.address != null  and queryParams.address != ''"> and address = #{queryParams.address}</if>
                        <if test="queryParams.governmentRegion != null  and queryParams.governmentRegion != ''"> and government_region = #{queryParams.governmentRegion}</if>
                        <if test="queryParams.status != null "> and status = #{queryParams.status}</if>
                        <if test="queryParams.createName != null  and queryParams.createName != ''"> and create_name like concat('%', #{queryParams.createName}, '%')</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag=0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
