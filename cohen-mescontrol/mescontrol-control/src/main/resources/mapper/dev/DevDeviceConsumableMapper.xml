<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceConsumableMapper">
    <select id="getDeviceConsumableList" resultType="com.cohen.control.dev.pojo.po.DevDeviceConsumablePO">
        SELECT ddc.*,
               dci.name as consumable_name,
               dci.consumable_type_name,
               dci.model,
               dci.unit
        FROM dev_device_consumable ddc
                 JOIN dev_consumable_info dci ON dci.id = ddc.consumable_id
        where ddc.device_id = #{deviceId}
    </select>
</mapper>
