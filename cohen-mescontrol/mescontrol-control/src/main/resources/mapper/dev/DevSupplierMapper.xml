<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevSupplierMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevSupplierPO">
        select * from dev_supplier
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.contactPerson != null  and queryParams.contactPerson != ''"> and contact_person = #{queryParams.contactPerson}</if>
                        <if test="queryParams.contactPhone != null  and queryParams.contactPhone != ''"> and contact_phone = #{queryParams.contactPhone}</if>
                        <if test="queryParams.cooperationDate != null "> and cooperation_date = #{queryParams.cooperationDate}</if>
                        <if test="queryParams.cooperationTag != null  and queryParams.cooperationTag != ''"> and cooperation_tag = #{queryParams.cooperationTag}</if>
                        <if test="queryParams.status != null "> and status = #{queryParams.status}</if>
                        <if test="queryParams.regionCode != null  and queryParams.regionCode != ''"> and region_code = #{queryParams.regionCode}</if>
                        <if test="queryParams.region != null  and queryParams.region != ''"> and region = #{queryParams.region}</if>
                        <if test="queryParams.description != null  and queryParams.description != ''"> and description = #{queryParams.description}</if>
                        <if test="queryParams.sort != null "> and sort = #{queryParams.sort}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
