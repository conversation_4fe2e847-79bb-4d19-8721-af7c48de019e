<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevMaintenanceRulesMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevMaintenanceRulesPO">
        select * from dev_maintenance_rules
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
                        <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and device_name like concat('%', #{queryParams.deviceName}, '%')</if>
                        <if test="queryParams.ruleContent != null  and queryParams.ruleContent != ''"> and rule_content = #{queryParams.ruleContent}</if>
                        <if test="queryParams.maintenanceCycle != null  and queryParams.maintenanceCycle != ''"> and maintenance_cycle = #{queryParams.maintenanceCycle}</if>
                        <if test="queryParams.maintenanceInterval != null  and queryParams.maintenanceInterval != ''"> and maintenance_interval = #{queryParams.maintenanceInterval}</if>
                        <if test="queryParams.advanceNotificationDays != null "> and advance_notification_days = #{queryParams.advanceNotificationDays}</if>
                        <if test="queryParams.notificationTime != null "> and notification_time = #{queryParams.notificationTime}</if>
                        <if test="queryParams.status != null "> and status = #{queryParams.status}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
