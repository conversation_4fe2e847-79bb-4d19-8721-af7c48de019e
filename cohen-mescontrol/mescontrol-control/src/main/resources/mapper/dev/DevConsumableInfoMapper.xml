<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInfoMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInfoPO">
        select * from dev_consumable_info
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.consumableTypeId != null "> and consumable_type_id = #{queryParams.consumableTypeId}</if>
                        <if test="queryParams.consumableTypeName != null  and queryParams.consumableTypeName != ''"> and consumable_type_name like concat('%', #{queryParams.consumableTypeName}, '%')</if>
                        <if test="queryParams.model != null  and queryParams.model != ''"> and model = #{queryParams.model}</if>
                        <if test="queryParams.unit != null  and queryParams.unit != ''"> and unit = #{queryParams.unit}</if>
                        <if test="queryParams.warningInventory != null "> and warning_inventory = #{queryParams.warningInventory}</if>
                        <if test="queryParams.brand != null  and queryParams.brand != ''"> and brand = #{queryParams.brand}</if>
                        <if test="queryParams.manufacturer != null  and queryParams.manufacturer != ''"> and manufacturer = #{queryParams.manufacturer}</if>
                        <if test="queryParams.picture != null  and queryParams.picture != ''"> and picture = #{queryParams.picture}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
