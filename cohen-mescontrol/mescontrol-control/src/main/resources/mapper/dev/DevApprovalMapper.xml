<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevApprovalMapper">

    <!-- 分页列表 -->
    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevApprovalPO">
        SELECT
        *
        FROM
        dev_approval m
        <where>
            and m.status != 0
            <if test="queryParams.approvalProcessType !=null">
                and m.approval_Process_Type = #{queryParams.approvalProcessType}
            </if>
            <if test="queryParams.status !=null">
                and m.`status` = #{queryParams.status}
            </if>
            <if test="queryParams.approverBy !=null">
                and m.approver_by = #{queryParams.approverBy}
            </if>
            <if test="queryParams.applyStartTime !=null and queryParams.applyEndTime!=null ">
                and m.create_time BETWEEN #{queryParams.applyStartTime} AND #{queryParams.applyEndTime}
            </if>
            <if test="queryParams.approverStartTime !=null and queryParams.approverEndTime!=null ">
                and m.approver_time BETWEEN #{queryParams.approverStartTime} AND #{queryParams.approverEndTime}
            </if>
        </where>
        GROUP BY m.id
        order by create_time desc
    </select>

    <!-- 查询详情 -->
    <select id="getDetail" resultType="com.cohen.control.dev.pojo.form.DevApprovalForm">
         SELECT
           *
        FROM
            dev_approval m
        WHERE id = #{id}
    </select>


    <select id="getLastBlzhApprovalDetail" resultType="com.cohen.control.dev.pojo.form.DevApprovalForm">
        SELECT
            *
        FROM
            dev_approval m
        WHERE approval_process_type = #{type} and business_id = #{businessId}
        AND approver_time &lt;= NOW() ORDER BY approver_time DESC LIMIT 1
    </select>

    <!--根据业务id查询详情-->
    <select id="getByBusinessId" resultType="com.cohen.control.dev.pojo.form.DevApprovalForm">
        SELECT
            *
        FROM
            dev_approval m
        WHERE approval_process_type = #{type} and business_id = #{businessId}
    </select>

    <select id="getLastEquipApprovalDetail" resultType="com.cohen.control.dev.pojo.form.DevApprovalForm">
        SELECT
            *
        FROM
            dev_approval m
        WHERE approval_process_type = #{type} and business_id = #{businessId}
          AND approver_time &lt;= NOW() ORDER BY approver_time DESC LIMIT 1
    </select>
</mapper>
