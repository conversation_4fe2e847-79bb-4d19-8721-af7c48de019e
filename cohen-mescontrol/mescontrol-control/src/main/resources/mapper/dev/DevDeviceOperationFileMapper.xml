<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceOperationFileMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceOperationFilePO">
        select * from dev_device_operation_file
        <where>
                        <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
                        <if test="queryParams.deviceNo != null  and queryParams.deviceNo != ''"> and device_no like concat('%', #{queryParams.deviceNo}, '%')</if>
                        <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and device_name like concat('%', #{queryParams.deviceName}, '%')</if>
                        <if test="queryParams.parameterDescription != null  and queryParams.parameterDescription != ''"> and parameter_description = #{queryParams.parameterDescription}</if>
                        <if test="queryParams.fileAccessPath != null  and queryParams.fileAccessPath != ''"> and file_access_path = #{queryParams.fileAccessPath}</if>
                        <if test="queryParams.fileName != null  and queryParams.fileName != ''"> and file_name like concat('%', #{queryParams.fileName}, '%')</if>
                        <if test="queryParams.fileAbsolutePath != null  and queryParams.fileAbsolutePath != ''"> and file_absolute_path = #{queryParams.fileAbsolutePath}</if>
                        <if test="queryParams.fileSize != null  and queryParams.fileSize != ''"> and file_size = #{queryParams.fileSize}</if>
                        <if test="queryParams.downloadCount != null "> and download_count = #{queryParams.downloadCount}</if>
                        <if test="queryParams.viewCount != null "> and view_count = #{queryParams.viewCount}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
