<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceMaintenanceRulesMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceMaintenanceRulesPO">
        SELECT
        dmr.id,
        dmr.device_id,
        dmr.rule_id,
        ed.name AS device_name,
        ed.device_type_id,
        ed.device_type_name,
        ed.`status`
        FROM
        dev_device_maintenance_rules dmr
        LEFT JOIN dev_device ed ON ed.id = dmr.device_id
        <where>
            and dmr.rule_id = #{queryParams.ruleId}
            <if test="queryParams.deviceName != null ">
                and ed.name like concat('%',#{queryParams.deviceName},'%')
            </if>
            <if test="queryParams.typeId != null ">
                and ed.device_type_id = #{queryParams.typeId}
            </if>
        </where>
        ORDER BY dmr.create_time DESC
    </select>


    <select id="notBindDeviceList" resultType="com.cohen.control.dev.pojo.po.DevDeviceMaintenanceRulesPO">
        SELECT
        ed.id AS device_id,
        ed.name AS device_name,
        ed.device_type_id,
        ed.device_type_name,
        ed.status
        FROM
        dev_device ed
        LEFT JOIN dev_device_maintenance_rules dmr
        ON ed.id = dmr.device_id
        <where>
            and ed.id not in
            (select dmr.device_id
            from dev_device_maintenance_rules
            where dmr.rule_id = #{queryParams.ruleId})
            <if test="queryParams.deviceName != null ">
                and ed.name like concat('%', #{queryParams.deviceName}, '%')
            </if>
            <if test="queryParams.typeId != null ">
                and ed.device_type_id = #{queryParams.typeId}
            </if>
        </where>
        ORDER BY
        ed.create_time DESC
    </select>


</mapper>
