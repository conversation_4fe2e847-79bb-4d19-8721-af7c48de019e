<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInoroutRecordMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInoroutRecordPO">
        select * from dev_consumable_inorout_record
        <where>
                        <if test="queryParams.code != null  and queryParams.code != ''"> and code = #{queryParams.code}</if>
                        <if test="queryParams.consumableId != null "> and consumable_id = #{queryParams.consumableId}</if>
                        <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
                        <if test="queryParams.warehouseId != null "> and warehouse_id = #{queryParams.warehouseId}</if>
                        <if test="queryParams.warehouseName != null  and queryParams.warehouseName != ''"> and warehouse_name like concat('%', #{queryParams.warehouseName}, '%')</if>
                        <if test="queryParams.type != null "> and type = #{queryParams.type}</if>
                        <if test="queryParams.businessId != null "> and business_id = #{queryParams.businessId}</if>
                        <if test="queryParams.businessName != null  and queryParams.businessName != ''"> and business_name like concat('%', #{queryParams.businessName}, '%')</if>
                        <if test="queryParams.num != null "> and num = #{queryParams.num}</if>
                        <if test="queryParams.unit != null "> and unit = #{queryParams.unit}</if>
                        <if test="queryParams.totalAmount != null "> and total_amount = #{queryParams.totalAmount}</if>
                        <if test="queryParams.operatorTime != null "> and operator_time = #{queryParams.operatorTime}</if>
                        <if test="queryParams.operator != null  and queryParams.operator != ''"> and operator = #{queryParams.operator}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
