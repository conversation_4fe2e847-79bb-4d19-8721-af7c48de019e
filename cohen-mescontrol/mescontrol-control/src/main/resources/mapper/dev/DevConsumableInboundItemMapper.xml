<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInboundItemMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInboundItemPO">
        select * from dev_consumable_inbound_item
        <where>
                        <if test="queryParams.inboundNo != null  and queryParams.inboundNo != ''"> and inbound_no = #{queryParams.inboundNo}</if>
                        <if test="queryParams.consumableId != null "> and consumable_id = #{queryParams.consumableId}</if>
                        <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
                        <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and batch_number = #{queryParams.batchNumber}</if>
                        <if test="queryParams.consumableTypeId != null "> and consumable_type_id = #{queryParams.consumableTypeId}</if>
                        <if test="queryParams.unit != null  and queryParams.unit != ''"> and unit = #{queryParams.unit}</if>
                        <if test="queryParams.model != null  and queryParams.model != ''"> and model = #{queryParams.model}</if>
                        <if test="queryParams.brand != null  and queryParams.brand != ''"> and brand = #{queryParams.brand}</if>
                        <if test="queryParams.manufacturer != null  and queryParams.manufacturer != ''"> and manufacturer = #{queryParams.manufacturer}</if>
                        <if test="queryParams.manufactureDate != null "> and manufacture_date = #{queryParams.manufactureDate}</if>
                        <if test="queryParams.expirationDate != null "> and expiration_date = #{queryParams.expirationDate}</if>
                        <if test="queryParams.intoNum != null "> and into_num = #{queryParams.intoNum}</if>
                        <if test="queryParams.price != null "> and price = #{queryParams.price}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="listInboundItemvo" resultType="com.cohen.control.dev.pojo.vo.DevConsumableInboundItemVO">
        select cii.*,info.consumable_type_name from dev_consumable_inbound_item cii
        left join dev_consumable_info info on cii.consumable_id = info.id
        <where>
            <if test="inboundNo != null  and inboundNo != ''"> and cii.inbound_no = #{inboundNo}</if>
            and cii.del_flag = 0
        </where>
        ORDER BY cii.create_time DESC
    </select>



</mapper>
