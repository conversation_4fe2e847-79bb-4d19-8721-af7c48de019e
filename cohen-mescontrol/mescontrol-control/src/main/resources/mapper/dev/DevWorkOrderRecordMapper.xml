<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevWorkOrderRecordMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderRecordPO">
        select * from dev_work_order_record
        <where>
                        <if test="queryParams.workOrderId != null "> and work_order_id = #{queryParams.workOrderId}</if>
                        <if test="queryParams.processorId != null "> and processor_id = #{queryParams.processorId}</if>
                        <if test="queryParams.processorName != null  and queryParams.processorName != ''"> and processor_name like concat('%', #{queryParams.processorName}, '%')</if>
                        <if test="queryParams.processorImage != null  and queryParams.processorImage != ''"> and processor_image = #{queryParams.processorImage}</if>
                        <if test="queryParams.processingResult != null  and queryParams.processingResult != ''"> and processing_result = #{queryParams.processingResult}</if>
                        <if test="queryParams.processingComment != null  and queryParams.processingComment != ''"> and processing_comment = #{queryParams.processingComment}</if>
                        <if test="queryParams.operationTime != null "> and operation_time = #{queryParams.operationTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
