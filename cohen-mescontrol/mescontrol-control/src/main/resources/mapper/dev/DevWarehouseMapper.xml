<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevWarehouseMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevWarehousePO">
        select * from dev_warehouse
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.region != null  and queryParams.region != ''"> and region = #{queryParams.region}</if>
                        <if test="queryParams.address != null  and queryParams.address != ''"> and address = #{queryParams.address}</if>
                        <if test="queryParams.longitude != null  and queryParams.longitude != ''"> and longitude = #{queryParams.longitude}</if>
                        <if test="queryParams.latitude != null  and queryParams.latitude != ''"> and latitude = #{queryParams.latitude}</if>
                        <if test="queryParams.manager != null  and queryParams.manager != ''"> and manager = #{queryParams.manager}</if>
                        <if test="queryParams.managerContact != null  and queryParams.managerContact != ''"> and manager_contact = #{queryParams.managerContact}</if>
                        <if test="queryParams.status != null "> and status = #{queryParams.status}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
