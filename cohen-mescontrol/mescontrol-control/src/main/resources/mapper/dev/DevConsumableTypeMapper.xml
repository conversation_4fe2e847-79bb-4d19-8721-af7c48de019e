<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableTypeMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableTypePO">
        select dct.*,dct2.name as parent_name from dev_consumable_type dct
        left join dev_consumable_type dct2 on dct2.id=dct.parent
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and dct.name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.parent != null "> and dct.parent = #{queryParams.parent}</if>
                        <if test="queryParams.acester != null  and queryParams.acester != ''"> and dct.acester = #{queryParams.acester}</if>
                        <if test="queryParams.sort != null "> and dct.sort = #{queryParams.sort}</if>
                        <if test="queryParams.status != null "> and dct.status = #{queryParams.status}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and dct.extra = #{queryParams.extra}</if>
            and dct.del_flag = 0
        </where>
        ORDER BY dct.create_time DESC
    </select>

</mapper>
