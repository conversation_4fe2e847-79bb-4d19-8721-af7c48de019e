<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceTypeMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceTypePO">
        select ddt.*,ddt2.name as parent_name from dev_device_type ddt
        left join dev_device_type ddt2  on ddt2.id=ddt.parent
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and ddt.name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.parent != null "> and ddt.parent = #{queryParams.parent}</if>
                        <if test="queryParams.acester != null  and queryParams.acester != ''"> and ddt.acester = #{queryParams.acester}</if>
                        <if test="queryParams.ancestors != null "> and ddt.ancestors = #{queryParams.ancestors}</if>
                        <if test="queryParams.status != null "> and ddt.status = #{queryParams.status}</if>
                        <if test="queryParams.parentName != null  and queryParams.parentName != ''"> and ddt2.name like concat('%',#{queryParams.parentName},'%') </if>
            and ddt.del_flag = 0
        </where>
        ORDER BY ddt.create_time DESC
    </select>

</mapper>
