<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableInventoryCheckMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableInventoryCheckPO">
        select * from dev_consumable_inventory_check
        <where>
                        <if test="queryParams.no != null  and queryParams.no != ''"> and no = #{queryParams.no}</if>
                        <if test="queryParams.warehouseId != null  and queryParams.warehouseId != ''"> and warehouse_id = #{queryParams.warehouseId}</if>
                        <if test="queryParams.warehouseName != null  and queryParams.warehouseName != ''"> and warehouse_name like concat('%', #{queryParams.warehouseName}, '%')</if>
                        <if test="queryParams.warehouseRegion != null  and queryParams.warehouseRegion != ''"> and warehouse_region = #{queryParams.warehouseRegion}</if>
                        <if test="queryParams.warehouseAddress != null  and queryParams.warehouseAddress != ''"> and warehouse_address = #{queryParams.warehouseAddress}</if>
                        <if test="queryParams.warehouseContact != null  and queryParams.warehouseContact != ''"> and warehouse_contact = #{queryParams.warehouseContact}</if>
                        <if test="queryParams.warehouseContactPhone != null  and queryParams.warehouseContactPhone != ''"> and warehouse_contact_phone = #{queryParams.warehouseContactPhone}</if>
                        <if test="queryParams.inventoryNum != null  and queryParams.inventoryNum != ''"> and inventory_num = #{queryParams.inventoryNum}</if>
                        <if test="queryParams.currentNum != null "> and current_num = #{queryParams.currentNum}</if>
                        <if test="queryParams.actualNum != null "> and actual_num = #{queryParams.actualNum}</if>
                        <if test="queryParams.differenceNum != null "> and difference_num = #{queryParams.differenceNum}</if>
                        <if test="queryParams.inventoryPersonnel != null  and queryParams.inventoryPersonnel != ''"> and inventory_personnel = #{queryParams.inventoryPersonnel}</if>
                        <if test="queryParams.inventoryTime != null "> and inventory_time = #{queryParams.inventoryTime}</if>
                        <if test="queryParams.inventoryStatus != null "> and inventory_status = #{queryParams.inventoryStatus}</if>
                        <if test="queryParams.verifyBy != null  and queryParams.verifyBy != ''"> and verify_by = #{queryParams.verifyBy}</if>
                        <if test="queryParams.verifyTime != null "> and verify_time = #{queryParams.verifyTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
