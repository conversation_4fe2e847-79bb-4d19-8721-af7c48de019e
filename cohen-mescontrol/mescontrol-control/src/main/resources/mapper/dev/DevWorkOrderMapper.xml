<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevWorkOrderMapper">

    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderPO">
        select ewo.* from dev_work_order ewo
        left join dev_device ed on ewo.device_id = ed.id

        <where>
            <if test="queryParams.stationId != null"> and ed.station_id = #{queryParams.stationId}</if>
            <if test="queryParams.title != null  and queryParams.title != ''"> and ewo.title = #{queryParams.title}</if>
            <if test="queryParams.type != null "> and ewo.type = #{queryParams.type}</if>   <if test="queryParams.type != null "> and ewo.type = #{queryParams.type}</if>
            <if test="queryParams.deviceId != null "> and ewo.device_id = #{queryParams.deviceId}</if>
            <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and ewo.device_name like concat('%', #{queryParams.deviceName}, '%')</if>
            <if test="queryParams.priority != null "> and ewo.priority = #{queryParams.priority}</if>
            <if test="queryParams.status != null "> and ewo.status = #{queryParams.status}</if>
            <if test="queryParams.approvalTime != null "> and ewo.approval_time = #{queryParams.approvalTime}</if>
            <if test="queryParams.reason != null  and queryParams.reason != ''"> and ewo.reason = #{queryParams.reason}</if>
            <if test="queryParams.completionTime != null "> and ewo.completion_time = #{queryParams.completionTime}</if>
            <if test="queryParams.processorId != null  and queryParams.processorId != ''"> and ewo.processor_id = #{queryParams.processorId}</if>
            <if test="queryParams.processorTime != null  and queryParams.processorTime != ''"> and ewo.processor_time = #{queryParams.processorTime}</if>
            <if test="queryParams.stationName != null"> and ed.station_name like concat('%', #{queryParams.stationName}, '%')</if>
            <if test="queryParams.startTime != null  and queryParams.startTime != ''">
                and date(ewo.start_time) &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null  and queryParams.endTime != ''">
                and date(ewo.start_time) &gt;= #{queryParams.endTime}
            </if>
            and ewo.del_flag = 0
        </where>
        ORDER BY ewo.create_time DESC
    </select>

    <select id="approvalLogPage" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderPO">
        select wo.id,
        wo.type,
        wo.title,
        wo.priority,
        wo.device_id,
        wo.device_name,
        wo.process_id,
        wo.process_name,
        wo.deadline,
        wo.status ,
        ea.approver_by as approvalBy,
        ea.approver_time as approvalTime,
        ea.approver_reson ,
        ea.status as approvalStatus
        from dev_work_order wo
        left join dev_approval ea on ea.business_id=wo.id
        <where>
            ea.approval_process_type=1 and ea.status !=0 and wo.del_flag=0
            <if test="queryParams.type != null ">
                and wo.type = #{queryParams.type}
            </if>
            <if test="queryParams.priority != null ">
                and wo.priority = #{queryParams.priority}
            </if>
        </where>
        order by wo.create_time desc
    </select>

   <select id="getApprovalList" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderPO">
        SELECT ewo.*
        from dev_work_order ewo
        left join dev_device ed on  ewo.device_id = ed.id
        <where>
            <if test="queryParams.type != null "> and ewo.type = #{queryParams.type}</if>
            <if test="queryParams.priority != null "> and ewo.priority = #{queryParams.priority}</if>
            <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and ewo.device_name like concat('%', #{queryParams.deviceName}, '%')</if>
            <if test="queryParams.code != null  and queryParams.code != ''"> and ed.code = #{queryParams.code}</if>
          and  ewo.status = 0
        </where>
        ORDER BY ed.create_time desc
    </select>

    <!--<select id="getMyApprovalList" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderPO">
       SELECT ewo.*,p.ID_ as procInsId,RES.ID_ as taskId
       FROM ACT_RU_TASK RES
       LEFT JOIN act_hi_procinst P ON P.ID_ = RES.PROC_INST_ID_
       LEFT JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_
       LEFT JOIN equip_work_order ewo ON ewo.id = p.BUSINESS_KEY_
       <where>
           RES.SUSPENSION_STATE_ = 1
           AND (
           RES.ASSIGNEE_ = #{queryParams.userId}
           OR (
           RES.ASSIGNEE_ IS NULL
           AND I.TYPE_ = 'candidate'
           AND (I.USER_ID_ = #{queryParams.userId} OR I.GROUP_ID_ IN (#{queryParams.groupIds}))))
           <if test="queryParams.type != null "> and ewo.type = #{queryParams.type}</if>
       </where>
       ORDER BY RES.CREATE_TIME_ DESC
   </select>

   <select id="getProcessedWorkOrders" resultType="com.cohen.control.dev.pojo.po.DevWorkOrderPO">
       select * from equip_work_order
       <where>
           <if test="queryParams.type != null ">
               and type = #{queryParams.type}
           </if>
           <if test="queryParams.processorId != null  and queryParams.processorId != ''">
               and processor_id = #{queryParams.processorId}
           </if>
           and del_flag = 0
           and status in (0,3)
       </where>
       ORDER BY processor_time DESC
   </select>-->
</mapper>
