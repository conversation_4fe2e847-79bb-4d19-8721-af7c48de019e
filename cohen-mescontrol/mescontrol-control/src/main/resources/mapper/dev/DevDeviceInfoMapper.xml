<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevDeviceInfoMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevDeviceInfoPO">
        select * from dev_device_info
        <where>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.typeId != null "> and type_id = #{queryParams.typeId}</if>
                        <if test="queryParams.typeName != null  and queryParams.typeName != ''"> and type_name like concat('%', #{queryParams.typeName}, '%')</if>
                        <if test="queryParams.model != null  and queryParams.model != ''"> and model = #{queryParams.model}</if>
                        <if test="queryParams.unit != null  and queryParams.unit != ''"> and unit = #{queryParams.unit}</if>
                        <if test="queryParams.brand != null  and queryParams.brand != ''"> and brand = #{queryParams.brand}</if>
                        <if test="queryParams.manufacturer != null "> and manufacturer = #{queryParams.manufacturer}</if>
                        <if test="queryParams.equipmentPictures != null  and queryParams.equipmentPictures != ''"> and equipment_pictures = #{queryParams.equipmentPictures}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
