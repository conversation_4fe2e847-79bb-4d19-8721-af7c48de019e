<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.dev.mapper.DevConsumableUseRecordMapper">


    <select id="listPages" resultType="com.cohen.control.dev.pojo.po.DevConsumableUseRecordPO">
        select dcur.*,info.consumable_type_name as consumableTypeName from dev_consumable_use_record dcur
        left join dev_consumable dc on dc.code =dcur.code
        left join dev_consumable_info info on dc.consumable_id = info.id

        <where>
            <if test="queryParams.code != null  and queryParams.code != ''"> and dcur.code = #{queryParams.code}</if>
            <if test="queryParams.planNo != null "> and dcur.plan_no = #{queryParams.planNo}</if>
            <if test="queryParams.planName != null  and queryParams.planName != ''"> and dcur.plan_name like concat('%', #{queryParams.planName}, '%')</if>
            <if test="queryParams.taskNo != null "> and dcur.task_no = #{queryParams.taskNo}</if>
            <if test="queryParams.taskName != null  and queryParams.taskName != ''"> and dcur.task_name like concat('%', #{queryParams.taskName}, '%')</if>
            <if test="queryParams.consumableName != null  and queryParams.consumableName != ''"> and dcur.consumable_name like concat('%', #{queryParams.consumableName}, '%')</if>
            <if test="queryParams.num != null "> and dcur.num = #{queryParams.num}</if>
            <if test="queryParams.model != null  and queryParams.model != ''"> and dcur.model = #{queryParams.model}</if>
            <if test="queryParams.unit != null  and queryParams.unit != ''"> and dcur.unit = #{queryParams.unit}</if>
            <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and dcur.batch_number = #{queryParams.batchNumber}</if>
            <if test="queryParams.operatorType != null  and queryParams.operatorType != ''"> and dcur.operator_type = #{queryParams.operatorType}</if>
            <if test="queryParams.operator != null  and queryParams.operator != ''"> and dcur.operator = #{queryParams.operator}</if>
            <if test="queryParams.startTime != null  and queryParams.startTime != ''">
                and date(dcur.operator_time) &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null  and queryParams.endTime != ''">
                and date(dcur.operator_time) &lt;= #{queryParams.endTime}
            </if>
            and dcur.del_flag = 0
        </where>
        ORDER BY dcur.create_time DESC
    </select>

</mapper>
