<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpProcedureMapper">
    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpProcedurePO">
        SELECT ep.*, COUNT(epi.id) AS process_count,sum(epi.task_duration) duration
        FROM `exp_procedure` ep
        LEFT JOIN exp_process_item epi ON epi.procedure_id = ep.id
        <where>
            <if test="queryParams.name != null  and queryParams.name != ''">
                and ep.name like concat('%', #{queryParams.name}, '%')
            </if>
            and ep.del_flag = 0 and epi.del_flag = 0
        </where>
        GROUP BY ep.id
        ORDER BY create_time DESC
    </select>
</mapper>
