<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpSampleItemMapper">

    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpSampleItemPO">
        select * from exp_sample_item
        <where>
                        <if test="queryParams.inboundNo != null  and queryParams.inboundNo != ''"> and inbound_no like concat('%',#{queryParams.inboundNo},'%') </if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
