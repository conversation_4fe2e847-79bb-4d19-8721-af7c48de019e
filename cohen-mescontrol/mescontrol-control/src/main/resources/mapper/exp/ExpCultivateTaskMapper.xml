<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCultivateTaskMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCultivateTaskPO">
        select * from exp_cultivate_task
        <where>
            <if test="queryParams.planNo != null  and queryParams.planNo != ''">
                and plan_no like concat('%',#{queryParams.planNo},'%')
            </if>
            <if test="queryParams.taskNo != null  and queryParams.taskNo != ''">
                and task_no like concat('%',#{queryParams.taskNo},'%')
            </if>
            <if test="queryParams.taskName != null  and queryParams.taskName != ''">
                and task_name like concat('%', #{queryParams.taskName}, '%')
            </if>
            <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''">
                and sample_no like concat('%', #{queryParams.sampleNo},'%')
            </if>
            <if test="queryParams.cellNo != null  and queryParams.cellNo != ''">
                and cell_no like concat('%',#{queryParams.cellNo},'%')
            </if>
            <if test="queryParams.deviceId != null ">
                and device_id = #{queryParams.deviceId}
            </if>
            <if test="queryParams.deviceName != null  and queryParams.deviceName != ''">
                and device_name like concat('%', #{queryParams.deviceName}, '%')
            </if>
            <if test="queryParams.responsiblePersonId != null ">
                and responsible_person_id = #{queryParams.responsiblePersonId}
            </if>
            <if test="queryParams.responsiblePersonName != null  and queryParams.responsiblePersonName != ''">
                and responsible_person_name like concat('%', #{queryParams.responsiblePersonName}, '%')
            </if>
            <if test="queryParams.taskStatus != null ">
                and task_status = #{queryParams.taskStatus}
            </if>
            <if test="queryParams.taskCreateTime != null ">
                and task_create_time = #{queryParams.taskCreateTime}
            </if>
            <if test="queryParams.taskCreateStartDate != null and queryParams.taskCreateStartDate != ''">
                and date(task_create_time) &gt;= #{queryParams.taskCreateStartDate}
            </if>
            <if test="queryParams.taskCreateEndDate != null and queryParams.taskCreateEndDate != ''">
                and date(task_create_time) &lt;= #{queryParams.taskCreateEndDate}
            </if>
            and del_flag = 0
        </where>
        ORDER BY task_create_time DESC,
        step_number ASC
    </select>

</mapper>
