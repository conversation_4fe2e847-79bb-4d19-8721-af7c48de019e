<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpSampleInboundMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpSampleInboundPO">
        select * from exp_sample_inbound
        <where>
                        <if test="queryParams.inboundNo != null  and queryParams.inboundNo != ''"> and inbound_no like concat('%',#{queryParams.inboundNo},'%')</if>
                        <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and batch_number like concat('%',#{queryParams.batchNumber},'%')</if>
                        <if test="queryParams.num != null "> and num = #{queryParams.num}</if>
                        <if test="queryParams.resource != null  and queryParams.resource != ''"> and resource like concat('%',#{queryParams.resource},'%')</if>
                        <if test="queryParams.resourceContact != null  and queryParams.resourceContact != ''"> and resource_contact = #{queryParams.resourceContact}</if>
                        <if test="queryParams.resourceContactPhone != null  and queryParams.resourceContactPhone != ''"> and resource_contact_phone = #{queryParams.resourceContactPhone}</if>
                        <if test="queryParams.receivingTime != null "> and receiving_time = #{queryParams.receivingTime}</if>
                        <if test="queryParams.operator != null  and queryParams.operator != ''"> and operator = #{queryParams.operator}</if>
                        <if test="queryParams.operatorContactPhone != null  and queryParams.operatorContactPhone != ''"> and operator_contact_phone = #{queryParams.operatorContactPhone}</if>
                        <if test="queryParams.operatorTime != null "> and operator_time = #{queryParams.operatorTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
                        <if test="queryParams.receivingStartDate != null  and queryParams.receivingStartDate != ''">
                            and receivingTime &gt;= #{queryParams.receivingStartDate}
                        </if>
                        <if test="queryParams.receivingEndDate != null  and queryParams.receivingEndDate!= ''">
                         and receivingTime &lt;= #{queryParams.receivingEndDate}
                        </if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
