<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpSampleMapper">

    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpSamplePO">
        select * from exp_sample
        <where>
                        <if test="queryParams.inboundNo != null  and queryParams.inboundNo != ''"> and inbound_no like concat('%',#{queryParams.inboundNo},'%') </if>
                        <if test="queryParams.batchNumber != null  and queryParams.batchNumber != ''"> and batch_number like concat('%',#{queryParams.batchNumber},'%') </if>
                        <if test="queryParams.num != null  and queryParams.num != ''"> and num = #{queryParams.num}</if>
                        <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''"> and sample_no like concat('%',#{queryParams.sampleNo},'%')</if>
                        <if test="queryParams.resource != null  and queryParams.resource != ''"> and resource like concat('%',#{queryParams.resource},'%') </if>
                        <if test="queryParams.resourceContact != null  and queryParams.resourceContact != ''"> and resource_contact = #{queryParams.resourceContact}</if>
                        <if test="queryParams.resourceContactPhone != null  and queryParams.resourceContactPhone != ''"> and resource_contact_phone = #{queryParams.resourceContactPhone}</if>
                        <if test="queryParams.planId != null "> and plan_id = #{queryParams.planId}</if>
                        <if test="queryParams.planName != null  and queryParams.planName != ''"> and plan_name like concat('%', #{queryParams.planName}, '%')</if>
                        <if test="queryParams.workId != null "> and work_id = #{queryParams.workId}</if>
                        <if test="queryParams.taskId != null "> and task_id = #{queryParams.taskId}</if>
                        <if test="queryParams.taskName != null  and queryParams.taskName != ''"> and task_name like concat('%', #{queryParams.taskName}, '%')</if>
                        <if test="queryParams.receivingTime != null "> and receiving_time = #{queryParams.receivingTime}</if>
                        <if test="queryParams.detectionStatus != null "> and detection_status = #{queryParams.detectionStatus}</if>
                        <if test="queryParams.detectionResult != null "> and detection_result = #{queryParams.detectionResult}</if>
                        <if test="queryParams.detectionTime != null "> and detection_time = #{queryParams.detectionTime}</if>
                        <if test="queryParams.productionStatus != null "> and production_status = #{queryParams.productionStatus}</if>
                        <if test="queryParams.productionStartTime != null "> and production_start_time = #{queryParams.productionStartTime}</if>
                        <if test="queryParams.productionEndTime != null "> and production_end_time = #{queryParams.productionEndTime}</if>
                        <if test="queryParams.productionDuration != null "> and production_duration = #{queryParams.productionDuration}</if>
                        <if test="queryParams.donorName != null  and queryParams.donorName != ''"> and donor_name like concat('%', #{queryParams.donorName}, '%')</if>
                        <if test="queryParams.donorAge != null "> and donor_age = #{queryParams.donorAge}</if>
                        <if test="queryParams.healthStatus != null  and queryParams.healthStatus != ''"> and health_status = #{queryParams.healthStatus}</if>
                        <if test="queryParams.donationTime != null "> and donation_time = #{queryParams.donationTime}</if>
                        <if test="queryParams.operator != null  and queryParams.operator != ''"> and operator = #{queryParams.operator}</if>
                        <if test="queryParams.operatorTime != null "> and operator_time = #{queryParams.operatorTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>


    <select id="indexCount" resultType="com.cohen.control.exp.pojo.vo.IndexCountAllVO">
        SELECT
            COALESCE((SELECT count(1) FROM exp_sample), 0) AS qidaiCount,
            COALESCE((SELECT sum(cell_quantity) FROM exp_cell_inventory), 0) AS xibaoCount,
            COALESCE((SELECT count(1) FROM exp_cultivate_report), 0) AS baogaoCount,
            COALESCE((SELECT count(1) FROM exp_cultivate_plan), 0) AS jihuaCount,
            COALESCE((SELECT count(1) FROM exp_cultivate_task), 0) AS renwuCount
    </select>

    <select id="xibaoShengChanStatistics" resultType="com.cohen.control.exp.pojo.vo.XibaoShengChanStatisticsVO">
        SELECT
            -- 计算合格数量
            COALESCE((SELECT SUM(CASE WHEN is_qualified = 1 THEN cell_quantity ELSE 0 END) FROM statistics_qualified_cell), 0) AS hegeCount,
            -- 计算总数量
            COALESCE((SELECT SUM(cell_quantity) FROM statistics_qualified_cell), 0) AS totalCount,
            -- 计算合格率，保留两位小数，防止总数量为0的情况
            CASE
                WHEN COALESCE((SELECT SUM(cell_quantity) FROM statistics_qualified_cell), 0) = 0 THEN 0
                ELSE
                    ROUND(
                                    (COALESCE((SELECT SUM(CASE WHEN is_qualified = 1 THEN cell_quantity ELSE 0 END) FROM statistics_qualified_cell), 0) * 1.0) /
                                    COALESCE((SELECT SUM(cell_quantity) FROM statistics_qualified_cell), 0) * 100, 2
                        )
                END AS hegelv,
            -- 计算报告数量
            COALESCE((SELECT COUNT(1) FROM exp_cultivate_report), 0) AS baogaoCount,
            -- 计算计划数量
            COALESCE((SELECT COUNT(1) FROM exp_cultivate_plan), 0) AS jihuaCount,
            -- 计算任务数量
            COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) AS renwuCount,
            -- 计算细胞库存总数
            COALESCE((SELECT SUM(cell_quantity) FROM exp_cell_inventory), 0) AS xibaoCount,

            -- 计算任务状态为 4（已完成）的任务百分比，保留两位小数
            CASE
                WHEN COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) = 0 THEN 0
                ELSE
                    ROUND(
                                    (COALESCE((SELECT COUNT(1) FROM exp_cultivate_task WHERE task_status = 4), 0) * 1.0) /
                                    COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) * 100, 2
                        )
                END AS renwuWanchengjindu
    </select>

    <select id="qidaiShengChanStatistics" resultType="com.cohen.control.exp.pojo.vo.QidaiShengChanStatisticsVO">
        SELECT
            -- 计算合格数量
            COALESCE((SELECT SUM(CASE WHEN detection_result = 1 THEN num ELSE 0 END) FROM exp_sample), 0) AS hegeCount,
            -- 计算总数量
            COALESCE((SELECT SUM(num) FROM exp_sample), 0) AS qidaiCount,
            -- 计算合格率，保留两位小数，防止总数量为0的情况
            CASE
                WHEN COALESCE((SELECT SUM(num) FROM exp_sample), 0) = 0 THEN 0
                ELSE
                    ROUND(
                                    (COALESCE((SELECT SUM(CASE WHEN detection_result = 1 THEN num ELSE 0 END) FROM exp_sample), 0) * 1.0) /
                                    COALESCE((SELECT SUM(num) FROM exp_sample), 0) * 100, 2
                        )
                END AS hegelv,
            -- 计算计划数量
            COALESCE((SELECT COUNT(1) FROM exp_cultivate_plan), 0) AS jihuaCount,
            -- 计算任务数量
            COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) AS renwuCount,

            -- 计算任务状态为 4（已完成）的任务百分比，保留两位小数
            CASE
                WHEN COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) = 0 THEN 0
                ELSE
                    ROUND(
                                    (COALESCE((SELECT COUNT(1) FROM exp_cultivate_task WHERE task_status = 4), 0) * 1.0) /
                                    COALESCE((SELECT COUNT(1) FROM exp_cultivate_task), 0) * 100, 2
                        )
                END AS renwuWanchengjindu
    </select>

</mapper>
