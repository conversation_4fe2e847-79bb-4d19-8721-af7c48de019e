<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCultivatePlanMapper">
    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCultivatePlanPO">
        SELECT
        p.id,
        p.no,
        p.name,
        p.status,
        p.create_time,
        GROUP_CONCAT(proc.process_name ORDER BY proc.sort SEPARATOR '、') AS process_names
        FROM
        exp_cultivate_plan p
        LEFT JOIN
        exp_cultivate_procedure proc ON p.id = proc.plan_id
        <where>
            <if test="queryParams.no != null  and queryParams.no != ''">
                and p.no = #{queryParams.no}
            </if>
            <if test="queryParams.name != null  and queryParams.name != ''">
                and p.name like concat('%', #{queryParams.name}, '%')
            </if>
            <if test="queryParams.status != null ">
                and p.status = #{queryParams.status}
            </if>
            <if test="queryParams.createTimeStart != null and queryParams.createTimeStart != ''">
                and date(p.create_time) &gt;= #{queryParams.createTimeStart}
            </if>
            <if test="queryParams.createTimeEnd != null and queryParams.createTimeEnd != ''">
                and date(p.create_time) &lt;= #{queryParams.createTimeEnd}
            </if>
            and p.del_flag = 0
        </where>
        GROUP BY p.id, p.no, p.name, p.status, p.create_time
        ORDER BY p.create_time DESC
    </select>

    <select id="selectProcedureList" resultType="com.cohen.control.exp.pojo.entity.ExpCultivateProcedure">
        select ecp.* from exp_cultivate_procedure  ecp
        inner join exp_cultivate_plan plan on plan.id=ecp.plan_id
        where ecp.del_flag = 0 and plan.status in (0,1)
    </select>

    <resultMap id="deviceParamsMap" type="java.util.Map">
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="code" property="deviceNo"/>
        <result column="status" property="deviceStatus"/>
        <result column="device_operation_parameter" property="deviceParams"/>
    </resultMap>

    <select id="getDeviceParams" resultMap="deviceParamsMap">
        <!--   SELECT pi.device_id,
           pi.device_name,
           d.`code`,
           d.status,
           pi.device_operation_parameter
           FROM exp_procedure ep
           LEFT JOIN exp_process_item pi ON pi.procedure_id = ep.id
           LEFT JOIN dev_device d on d.id = pi.device_id
           WHERE ep.id = #{processId}
           AND pi.del_flag=0
           AND ep.del_flag=0
           GROUP BY pi.device_id, d.`code`-->
        WITH RankedData AS (
        SELECT
        pi.id,
        pi.device_id,
        pi.device_name,
        d.`code`,
        d.status,
        pi.device_operation_parameter,
        ROW_NUMBER() OVER (
        PARTITION BY pi.device_id, d.`code`
        ORDER BY pi.sort ASC
        ) AS rn
        FROM
        exp_procedure ep
        JOIN
        exp_process_item pi ON pi.procedure_id = ep.id
        JOIN
        dev_device d ON d.id = pi.device_id
        WHERE
        ep.id = #{processId}
        AND pi.del_flag = 0
        AND ep.del_flag = 0
        )
        SELECT
        id,
        device_id,
        device_name,
        `code`,
        status,
        device_operation_parameter
        FROM
        RankedData
        WHERE
        rn = 1;  -- 仅选择每个分组的第一个记录

    </select>

    <select id="getNearestPlanToCurrentProcess" resultType="com.cohen.control.exp.pojo.entity.ExpCultivatePlan">
        SELECT cp.*
        FROM exp_cultivate_plan cp
                 LEFT JOIN exp_cultivate_procedure ecp ON ecp.plan_id = cp.id
        WHERE ecp.process_id = #{processId}
          AND cp.del_flag = 0
        ORDER BY ecp.start_time DESC
            LIMIT 1;
    </select>
</mapper>
