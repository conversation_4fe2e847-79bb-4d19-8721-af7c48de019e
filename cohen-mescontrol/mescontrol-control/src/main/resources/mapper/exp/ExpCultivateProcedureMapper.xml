<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCultivateProcedureMapper">

    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCultivateProcedurePO">
        select * from exp_cultivate_procedure
        <where>
                        <if test="queryParams.planId != null "> and plan_id = #{queryParams.planId}</if>
                        <if test="queryParams.planNo != null  and queryParams.planNo != ''"> and plan_no = #{queryParams.planNo}</if>
                        <if test="queryParams.planName != null  and queryParams.planName != ''"> and plan_name like concat('%', #{queryParams.planName}, '%')</if>
                        <if test="queryParams.processName != null  and queryParams.processName != ''"> and process_name like concat('%', #{queryParams.processName}, '%')</if>
                        <if test="queryParams.processQuantity != null "> and process_quantity = #{queryParams.processQuantity}</if>
                        <if test="queryParams.taskDuration != null "> and task_duration = #{queryParams.taskDuration}</if>
                        <if test="queryParams.startTime != null "> and start_time = #{queryParams.startTime}</if>
                        <if test="queryParams.endTime != null "> and end_time = #{queryParams.endTime}</if>
                        <if test="queryParams.sort != null "> and sort = #{queryParams.sort}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
