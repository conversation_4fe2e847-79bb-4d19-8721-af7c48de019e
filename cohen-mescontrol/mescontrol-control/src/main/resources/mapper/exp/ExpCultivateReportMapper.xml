<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCultivateReportMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCultivateReportPO">
        select * from exp_cultivate_report
        <where>
                        <if test="queryParams.reportNo != null  and queryParams.reportNo != ''"> and report_no = #{queryParams.reportNo}</if>
                        <if test="queryParams.planNo != null  and queryParams.planNo != ''"> and plan_no = #{queryParams.planNo}</if>
                        <if test="queryParams.planName != null  and queryParams.planName != ''"> and plan_name like concat('%', #{queryParams.planName}, '%')</if>
                        <if test="queryParams.taskNo != null  and queryParams.taskNo != ''"> and task_no = #{queryParams.taskNo}</if>
                        <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''"> and sample_no = #{queryParams.sampleNo}</if>
                        <if test="queryParams.cellNo != null  and queryParams.cellNo != ''"> and cell_no = #{queryParams.cellNo}</if>
                        <if test="queryParams.detectionTime != null "> and detection_time = #{queryParams.detectionTime}</if>
                        <if test="queryParams.detectionDuration != null "> and detection_duration = #{queryParams.detectionDuration}</if>
                        <if test="queryParams.detectionResult != null  and queryParams.detectionResult != ''"> and detection_result = #{queryParams.detectionResult}</if>
                        <if test="queryParams.detectionValue != null  and queryParams.detectionValue != ''"> and detection_value = #{queryParams.detectionValue}</if>
                        <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
                        <if test="queryParams.deviceNo != null  and queryParams.deviceNo != ''"> and device_no = #{queryParams.deviceNo}</if>
                        <if test="queryParams.responsiblePersonId != null "> and responsible_person_id = #{queryParams.responsiblePersonId}</if>
                        <if test="queryParams.detectionStartTime != null "> and detection_start_time = #{queryParams.detectionStartTime}</if>
                        <if test="queryParams.detectionEndTime != null "> and detection_end_time = #{queryParams.detectionEndTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
