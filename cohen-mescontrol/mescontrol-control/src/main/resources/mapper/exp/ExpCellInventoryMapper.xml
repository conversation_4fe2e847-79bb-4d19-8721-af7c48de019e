<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCellInventoryMapper">

    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCellInventoryPO">
        select * from exp_cell_inventory
        <where>
            <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''"> and sample_no = #{queryParams.sampleNo}</if>
            <if test="queryParams.cellNo != null  and queryParams.cellNo != ''"> and cell_no = #{queryParams.cellNo}</if>
            <if test="queryParams.storageTime != null "> and storage_time = #{queryParams.storageTime}</if>
            <if test="queryParams.productionStartTime != null "> and production_start_time = #{queryParams.productionStartTime}</if>
            <if test="queryParams.productionEndTime != null "> and production_end_time = #{queryParams.productionEndTime}</if>
            <if test="queryParams.productionDuration != null  and queryParams.productionDuration != ''"> and production_duration = #{queryParams.productionDuration}</if>
            <if test="queryParams.storageLocation != null  and queryParams.storageLocation != ''"> and storage_location = #{queryParams.storageLocation}</if>
            <if test="queryParams.cellQuantity != null "> and cell_quantity = #{queryParams.cellQuantity}</if>
            <if test="queryParams.outboundStatus != null "> and outbound_status = #{queryParams.outboundStatus}</if>
            <if test="queryParams.cellStatus != null "> and cell_status = #{queryParams.cellStatus}</if>
            <if test="queryParams.rukuStartDate != null  and queryParams.rukuStartDate != ''">
                and date(storage_time) &gt;= #{queryParams.rukuStartDate}
            </if>
            <if test="queryParams.rukuEndDate != null  and queryParams.rukuEndDate != ''">
                and date(storage_time) &lt;= #{queryParams.rukuStartDate}
            </if>

            <if test="queryParams.chukuStartDate != null  and queryParams.chukuStartDate != ''">
                and date(outbound_time) &gt;= #{queryParams.chukuStartDate}
            </if>
            <if test="queryParams.chukuEndDate != null  and queryParams.chukuEndDate != ''">
                and date(outbound_time) &lt;= #{queryParams.chukuEndDate}
            </if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
