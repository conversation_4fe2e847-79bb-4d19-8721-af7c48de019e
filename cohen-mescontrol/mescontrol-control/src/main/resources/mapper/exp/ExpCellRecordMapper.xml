<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCellRecordMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCellRecordPO">
        select * from exp_cell_record
        <where>
                        <if test="queryParams.sampleNo != null  and queryParams.sampleNo != ''"> and sample_no = #{queryParams.sampleNo}</if>
                        <if test="queryParams.cellNo != null  and queryParams.cellNo != ''"> and cell_no = #{queryParams.cellNo}</if>
                        <if test="queryParams.processNode != null  and queryParams.processNode != ''"> and process_node = #{queryParams.processNode}</if>
                        <if test="queryParams.processDescription != null  and queryParams.processDescription != ''"> and process_description = #{queryParams.processDescription}</if>
                        <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
                        <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and device_name like concat('%', #{queryParams.deviceName}, '%')</if>
                        <if test="queryParams.operator != null  and queryParams.operator != ''"> and operator = #{queryParams.operator}</if>
                        <if test="queryParams.taskCreationTime != null "> and task_creation_time = #{queryParams.taskCreationTime}</if>
                        <if test="queryParams.taskCompletionTime != null "> and task_completion_time = #{queryParams.taskCompletionTime}</if>
                        <if test="queryParams.taskDuration != null "> and task_duration = #{queryParams.taskDuration}</if>
                        <if test="queryParams.taskResult != null  and queryParams.taskResult != ''"> and task_result = #{queryParams.taskResult}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
