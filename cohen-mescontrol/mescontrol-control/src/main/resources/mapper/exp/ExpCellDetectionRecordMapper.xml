<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCellDetectionRecordMapper">


    <select id="getDetectionList" resultType="com.cohen.control.exp.pojo.po.ExpCellDetectionRecordPO">
        select * from exp_cell_detection_record
        <where>
            <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
            <if test="queryParams.batchno != null  and queryParams.batchno != ''"> and batchno like concat('%', #{queryParams.batchno}, '%')</if>
            <if test="queryParams.id != null "> and id = #{queryParams.id}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
