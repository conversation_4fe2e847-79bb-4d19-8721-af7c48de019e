<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpProcessItemMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpProcessItemPO">
        select * from exp_process_item
        <where>
                        <if test="queryParams.procedureId != null "> and procedure_id = #{queryParams.procedureId}</if>
                        <if test="queryParams.procedureName != null  and queryParams.procedureName != ''"> and procedure_name like concat('%', #{queryParams.procedureName}, '%')</if>
                        <if test="queryParams.sort != null "> and sort = #{queryParams.sort}</if>
                        <if test="queryParams.no != null  and queryParams.no != ''"> and no = #{queryParams.no}</if>
                        <if test="queryParams.name != null  and queryParams.name != ''"> and name like concat('%', #{queryParams.name}, '%')</if>
                        <if test="queryParams.descirption != null  and queryParams.descirption != ''"> and descirption = #{queryParams.descirption}</if>
                        <if test="queryParams.deviceId != null "> and device_id = #{queryParams.deviceId}</if>
                        <if test="queryParams.deviceName != null  and queryParams.deviceName != ''"> and device_name like concat('%', #{queryParams.deviceName}, '%')</if>
                        <if test="queryParams.deviceOperationParameter != null  and queryParams.deviceOperationParameter != ''"> and device_operation_parameter = #{queryParams.deviceOperationParameter}</if>
                        <if test="queryParams.fileName != null  and queryParams.fileName != ''"> and file_name like concat('%', #{queryParams.fileName}, '%')</if>
                        <if test="queryParams.taskDuration != null "> and task_duration = #{queryParams.taskDuration}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

    <resultMap id="deviceParamsMap" type="java.util.Map">
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="code" property="deviceNo"/>
        <result column="status" property="deviceStatus"/>
        <result column="device_operation_parameter" property="deviceParams"/>
    </resultMap>


    <select id="getProcessDeivceParams" resultMap="deviceParamsMap">
        select epi.device_id, epi.device_name, dd.code,dd.status, epi.device_operation_parameter
        from exp_process_item epi
                 left join dev_device dd on dd.id = epi.device_id
        where epi.del_flag = 0
          and epi.device_id = #{deviceId}
          and epi.procedure_id = #{procedureId}
          and epi.sort = #{stepNumber}
    </select>
</mapper>
