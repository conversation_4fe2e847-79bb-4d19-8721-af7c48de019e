<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.ExpCellOutboundRecordMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.ExpCellOutboundRecordPO">
        select * from exp_cell_outbound_record
        <where>
                        <if test="queryParams.cellNo != null  and queryParams.cellNo != ''"> and cell_no = #{queryParams.cellNo}</if>
                        <if test="queryParams.quantity != null  and queryParams.quantity != ''"> and quantity = #{queryParams.quantity}</if>
                        <if test="queryParams.destination != null  and queryParams.destination != ''"> and destination = #{queryParams.destination}</if>
                        <if test="queryParams.hospital != null  and queryParams.hospital != ''"> and hospital = #{queryParams.hospital}</if>
                        <if test="queryParams.proof != null  and queryParams.proof != ''"> and proof = #{queryParams.proof}</if>
                        <if test="queryParams.outboundResponsible != null  and queryParams.outboundResponsible != ''"> and outbound_responsible = #{queryParams.outboundResponsible}</if>
                        <if test="queryParams.responsibleContact != null  and queryParams.responsibleContact != ''"> and responsible_contact = #{queryParams.responsibleContact}</if>
                        <if test="queryParams.status != null "> and status = #{queryParams.status}</if>
                        <if test="queryParams.purpose != null  and queryParams.purpose != ''"> and purpose = #{queryParams.purpose}</if>
                        <if test="queryParams.outboundTime != null "> and outbound_time = #{queryParams.outboundTime}</if>
                        <if test="queryParams.extra != null  and queryParams.extra != ''"> and extra = #{queryParams.extra}</if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
