<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.StatisticsAlarmMapper">


    <select id="listPages" resultType="com.cohen.control.exp.pojo.po.StatisticsAlarmPO">
        select * from statistics_alarm
        <where>
            <if test="queryParams.businessId != null  and queryParams.businessId != ''"> and business_id like concat('%',#{queryParams.businessId},'%') </if>
            <if test="queryParams.alarmType != null  "> and alarmType = #{queryParams.alarmType}</if>
            <if test="queryParams.alarmStatus != null "> and alarm_status = #{queryParams.alarmStatus}</if>

            <if test="queryParams.startTime != null  and queryParams.startTime != ''">
                and date(alarm_time) &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null  and queryParams.endTime != ''">
                and date(alarm_time) &lt;= #{queryParams.endTime}
            </if>
            and del_flag = 0
        </where>
        ORDER BY create_time DESC
    </select>



    <select id="indexTubiao" resultType="com.cohen.control.exp.pojo.vo.IndexStatisticsAlarmVO">
        SELECT
            DATE(check_date) AS check_date,
            SUM(CASE WHEN is_qualified = 1 THEN cell_quantity ELSE 0 END) AS qualified_cells,
            SUM(CASE WHEN is_qualified = 2 THEN cell_quantity ELSE 0 END) AS unqualified_cells
        FROM
            statistics_qualified_cell
        WHERE
            DATE(check_date) >= CURDATE() - INTERVAL 7 DAY
            and type = 1
        GROUP BY
            DATE(check_date)
        ORDER BY
            check_date;
    </select>

    <select id="qidaiTubiao" resultType="com.cohen.control.exp.pojo.vo.IndexStatisticsAlarmVO">
        SELECT
            DATE(check_date) AS check_date,
            SUM(CASE WHEN is_qualified = 1 THEN cell_quantity ELSE 0 END) AS qualified_cells,
            SUM(CASE WHEN is_qualified = 2 THEN cell_quantity ELSE 0 END) AS unqualified_cells,
            temperature as wendu
        FROM
            statistics_qualified_cell
        WHERE
            DATE(check_date) >= CURDATE() - INTERVAL 7 DAY
            and type = 2
        GROUP BY
            DATE(check_date)
        ORDER BY
            check_date;
    </select>

    <select id="gaojingdengjiqushi" resultType="com.cohen.control.exp.pojo.vo.IndexStatisticsAlarmQushiVO">
        WITH date_range AS (
            SELECT CURDATE() - INTERVAL n DAY AS checkDate
        FROM (
            SELECT 0 AS n UNION ALL
            SELECT 1 UNION ALL
            SELECT 2 UNION ALL
            SELECT 3 UNION ALL
            SELECT 4 UNION ALL
            SELECT 5 UNION ALL
            SELECT 6
            ) AS days
            )
        SELECT
            dr.checkDate,  -- 日期
            COALESCE(COUNT(CASE WHEN a.alarm_status = 3 THEN 1 END), 0) AS gao,    -- 高等级告警数量，缺失时为0
            COALESCE(COUNT(CASE WHEN a.alarm_status = 2 THEN 1 END), 0) AS zhong,  -- 中等级告警数量，缺失时为0
            COALESCE(COUNT(CASE WHEN a.alarm_status = 1 THEN 1 END), 0) AS di     -- 低等级告警数量，缺失时为0
        FROM
            date_range dr
                LEFT JOIN
            statistics_alarm a ON DATE(a.alarm_time) = dr.checkDate AND a.alarm_time >= CURDATE() - INTERVAL 7 DAY
        GROUP BY
            dr.checkDate
        ORDER BY
            dr.checkDate DESC;  -- 按日期倒序排列
    </select>


</mapper>
