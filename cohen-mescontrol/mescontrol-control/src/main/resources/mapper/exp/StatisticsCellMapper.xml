<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cohen.control.exp.mapper.StatisticsCellMapper">


    <select id="indexXibaoQushi" resultType="com.cohen.control.exp.pojo.vo.IndexStatisticsCellVO">
        SELECT
            DATE_FORMAT(data_push_time, '%H:%i') AS time_slot,  -- 格式化时间为每五分钟一个时间段
            SUM(cell_quantity) AS total_cell_quantity,                   -- 累计细胞数量
            AVG(temperature) AS avg_temperature                          -- 平均温度
        FROM
            statistics_cell
        WHERE
            data_push_time >= NOW() - INTERVAL 1 HOUR                   -- 获取最近1小时的数据
        GROUP BY
            UNIX_TIMESTAMP(data_push_time) DIV 300                      -- 每5分钟一个时间段
        ORDER BY
            data_push_time DESC                                         -- 按时间降序排序
            LIMIT 12;                                                         -- 获取最近的12条记录
    </select>



</mapper>
