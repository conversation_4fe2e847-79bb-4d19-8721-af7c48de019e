server:
  port: 6602

spring:
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: http://172.20.47.139:8848
        namespace: gsdx-mes
      # 配置中心
      config:
        server-addr: http://172.20.47.139:8848
        namespace: gsdx-mes
        file-extension: yaml
        shared-configs[0]:
          data-id: cohen-common.yaml
          namespace: gsdx-mes
          refresh: true

logging:
  level:
    root: INFO
    org.springframework.web: INFO
    org.springframework.security: TRACE
    org.springframework.security.oauth2: TRACE
    #org.springframework.boot.autoconfigure: DEBUG