server:
  port: 6602
spring:
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    nacos:
      # 注册中心
      discovery:
        server-addr: http://172.20.47.139:8848
        namespace: ming-test
      # 配置中心
      config:
        server-addr: http://172.20.47.139:8848
        namespace: ming-test
        file-extension: yaml
        shared-configs[0]:
          data-id: cohen-common.yaml
          namespace: ming-test
          refresh: true

logging:
  level:
    root: INFO
    org.springframework.web: INFO
    org.springframework.security: TRACE
    org.springframework.security.oauth2: TRACE
#    org.springframework.boot.autoconfigure: DEBUG

bluetooth:
  pemPath: D:\10-py\01test\private_key.pem
