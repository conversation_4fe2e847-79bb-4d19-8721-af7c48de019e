package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpProcessItemForm;
import com.cohen.control.exp.pojo.query.ExpProcessItemPageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcessItemVO;
import com.cohen.control.exp.service.ExpProcessItemService;


/**
 * 工序项Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "工序项接口")
@RestController
@RequestMapping("/api/v1/exp/item")
@RequiredArgsConstructor
public class ExpProcessItemController
{
    private final ExpProcessItemService expProcessItemService;

    @ApiOperation(value = "工序项分页列表")
    @GetMapping("/pages")
        public PageResult listExpProcessItemPages(ExpProcessItemPageQuery queryParams){
            IPage<ExpProcessItemVO> result = expProcessItemService.listExpProcessItemPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "工序项表单数据")
    @PreAuthorize("@pms.hasPermission('exp:item:detail')")
    @GetMapping("/{id}/form")
    public Result getExpProcessItemDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpProcessItemVO vo = expProcessItemService.getExpProcessItemData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增工序项")
    @Log(title = "新增工序项",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:item:add')")
    @PostMapping
    public Result saveExpProcessItem(
            @RequestBody @Valid ExpProcessItemForm form
    ) {
        boolean result = expProcessItemService.saveExpProcessItem(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改工序项")
    @Log(title = "修改工序项",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:item:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpProcessItem(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpProcessItemForm form
    ) {
        boolean result = expProcessItemService.updateExpProcessItem(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除工序项")
    @Log(title = "删除工序项",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:item:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpProcessItem(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expProcessItemService.deleteExpProcessItem(ids);
        return Result.judge(result);
    }


}
