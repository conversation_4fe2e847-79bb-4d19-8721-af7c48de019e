package com.cohen.control.dev.pojo.vo;

import lombok.Data;

@Data
public class RobotSystemDataVO {

   // private String dmc;
    private Boolean start;
    private Boolean stop;
    private Boolean pause;
    private Boolean running;
    private Short processinfoIdx0;
    private Boolean processinfoStart0;
    private Short processinfoIdx1;
    private Boolean processinfoStart1;
    private Short processinfoIdx2;
    private Boolean processinfoStart2;
    private Short processinfoIdx3;
    private Boolean processinfoStart3;
    private Short processinfoIdx4;
    private Boolean processinfoStart4;
    private Short processinfoIdx5;
    private Boolean processinfoStart5;
    private Short processinfoIdx6;
    private Boolean processinfoStart6;
    private Short processinfoIdx7;
    private Boolean processinfoStart7;
    private Short processinfoIdx8;
    private Boolean processinfoStart8;
    private Short processinfoIdx9;
    private Boolean processinfoStart9;
    private Short processinfoIdx10;
    private Boolean processinfoStart10;
    private Short processinfoIdx11;
    private Boolean processinfoStart11;
    private Short processinfoIdx12;
    private Boolean processinfoStart12;
    private Short processinfoIdx13;
    private Boolean processinfoStart13;
    private Short processinfoIdx14;
    private Boolean processinfoStart14;
    private Short processinfoIdx15;
    private Boolean processinfoStart15;
    private Short processinfoIdx16;
    private Boolean processinfoStart16;
    private Short processinfoIdx17;
    private Boolean processinfoStart17;
    private Short processinfoIdx18;
    private Boolean processinfoStart18;
    private Short processinfoIdx19;
    private Boolean processinfoStart19;
    private Short processinfoIdx20;
    private Boolean processinfoStart20;
    private Short processinfoIdx21;
    private Boolean processinfoStart21;
    private Short processinfoIdx22;
    private Boolean processinfoStart22;
    private Short processinfoIdx23;
    private Boolean processinfoStart23;
    private Short processinfoIdx24;
    private Boolean processinfoStart24;
    private Short processinfoIdx25;
    private Boolean processinfoStart25;
    private Short processinfoIdx26;
    private Boolean processinfoStart26;
    private Short processinfoIdx27;
    private Boolean processinfoStart27;
    private Short processinfoIdx28;
    private Boolean processinfoStart28;
    private Short processinfoIdx29;
    private Boolean processinfoStart29;
    private Short processinfoIdx30;
    private Boolean processinfoStart30;
    private Short processinfoIdx31;
    private Boolean processinfoStart31;
    private Short processinfoIdx32;
    private Boolean processinfoStart32;
    private Short processinfoIdx33;
    private Boolean processinfoStart33;
    private Short processinfoIdx34;
    private Boolean processinfoStart34;
    private Short processinfoIdx35;
    private Boolean processinfoStart35;
    private Short processinfoIdx36;
    private Boolean processinfoStart36;
    private Short processinfoIdx37;
    private Boolean processinfoStart37;
    private Short processinfoIdx38;
    private Boolean processinfoStart38;
    private Short processinfoIdx39;
    private Boolean processinfoStart39;
    private Short processinfoIdx40;
    private Boolean processinfoStart40;
    private Short processinfoIdx41;
    private Boolean processinfoStart41;
    private Short processinfoIdx42;
    private Boolean processinfoStart42;
    private Short processinfoIdx43;
    private Boolean processinfoStart43;
    private Short processinfoIdx44;
    private Boolean processinfoStart44;

    private Short robot1PoweredOn;
    private Boolean robot1Enabled;
    private Boolean robot1Paused;
    private Boolean robot1Inpos;
    private Short robot1InterpState;
    private Short robot1TaskState;
    private Short robot1CurrentToolId;
    private Short robot1CurrentUserId;
    private Float robot1ActualRx;
    private Float robot1ActualRy;
    private Float robot1ActualRz;
    private Float robot1ActualX;
    private Float robot1ActualY;
    private Float robot1ActualZ;
    private Float robot1CommandRx;
    private Float robot1CommandRy;
    private Float robot1CommandRz;
    private Float robot1CommandX;
    private Float robot1CommandY;
    private Float robot1CommandZ;
    private Float robot1ActualJoint1;
    private Float robot1ActualJoint2;
    private Float robot1ActualJoint3;
    private Float robot1ActualJoint4;
    private Float robot1ActualJoint5;
    private Float robot1ActualJoint6;
    private Float robot1CommandJoint1;
    private Float robot1CommandJoint2;
    private Float robot1CommandJoint3;
    private Float robot1CommandJoint4;
    private Float robot1CommandJoint5;
    private Float robot1CommandJoint6;

    private Short robot2PoweredOn;
    private Boolean robot2Enabled;
    private Boolean robot2Paused;
    private Boolean robot2Inpos;
    private Short robot2InterpState;
    private Short robot2TaskState;
    private Short robot2CurrentToolId;
    private Short robot2CurrentUserId;
    private Float robot2ActualRx;
    private Float robot2ActualRy;
    private Float robot2ActualRz;
    private Float robot2ActualX;
    private Float robot2ActualY;
    private Float robot2ActualZ;
    private Float robot2CommandRx;
    private Float robot2CommandRy;
    private Float robot2CommandRz;
    private Float robot2CommandX;
    private Float robot2CommandY;
    private Float robot2CommandZ;
    private Float robot2ActualJoint1;
    private Float robot2ActualJoint2;
    private Float robot2ActualJoint3;
    private Float robot2ActualJoint4;
    private Float robot2ActualJoint5;
    private Float robot2ActualJoint6;
    private Float robot2CommandJoint1;
    private Float robot2CommandJoint2;
    private Float robot2CommandJoint3;
    private Float robot2CommandJoint4;
    private Float robot2CommandJoint5;
    private Float robot2CommandJoint6;

    private Short robot3PoweredOn;
    private Boolean robot3Enabled;
    private Boolean robot3Paused;
    private Boolean robot3Inpos;
    private Short robot3InterpState;
    private Short robot3TaskState;
    private Short robot3CurrentToolId;
    private Short robot3CurrentUserId;
    private Float robot3ActualRx;
    private Float robot3ActualRy;
    private Float robot3ActualRz;
    private Float robot3ActualX;
    private Float robot3ActualY;
    private Float robot3ActualZ;
    private Float robot3CommandRx;
    private Float robot3CommandRy;
    private Float robot3CommandRz;
    private Float robot3CommandX;
    private Float robot3CommandY;
    private Float robot3CommandZ;
    private Float robot3ActualJoint1;
    private Float robot3ActualJoint2;
    private Float robot3ActualJoint3;
    private Float robot3ActualJoint4;
    private Float robot3ActualJoint5;
    private Float robot3ActualJoint6;
    private Float robot3CommandJoint1;
    private Float robot3CommandJoint2;
    private Float robot3CommandJoint3;
    private Float robot3CommandJoint4;
    private Float robot3CommandJoint5;
    private Float robot3CommandJoint6;
}
