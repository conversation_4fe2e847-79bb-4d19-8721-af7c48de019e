package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.utils.RandomLabelUtils;
import com.cohen.control.exp.converter.ExpProcedureConsumableConverter;
import com.cohen.control.exp.converter.ExpProcedureConverter;
import com.cohen.control.exp.converter.ExpProcessItemConverter;
import com.cohen.control.exp.mapper.ExpProcedureConsumableMapper;
import com.cohen.control.exp.pojo.dto.TaskDTO;
import com.cohen.control.exp.pojo.entity.ExpProcedureConsumable;
import com.cohen.control.exp.pojo.entity.ExpProcessItem;
import com.cohen.control.exp.pojo.form.ExpProcedureConsumableForm;
import com.cohen.control.exp.pojo.form.ExpProcessItemForm;
import com.cohen.control.exp.pojo.vo.ExpProcedureConsumableVO;
import com.cohen.control.exp.pojo.vo.ExpProcessItemVO;
import com.cohen.control.exp.service.ExpCultivatePlanService;
import com.cohen.control.exp.service.ExpProcedureConsumableService;
import com.cohen.control.exp.service.ExpProcedureService;
import com.cohen.control.exp.service.ExpProcessItemService;
import com.cohen.system.notice.dto.WsQueueMessageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpProcedureMapper;
import com.cohen.control.exp.pojo.entity.ExpProcedure;
import com.cohen.control.exp.pojo.form.ExpProcedureForm;
import com.cohen.control.exp.pojo.po.ExpProcedurePO;
import com.cohen.control.exp.pojo.query.ExpProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcedureVO;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工序流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpProcedureServiceImpl extends ServiceImpl<ExpProcedureMapper, ExpProcedure>implements ExpProcedureService {

    private final ExpProcedureConverter expProcedureConverter;
    private final ExpProcessItemConverter expProcessItemConverter;
    private final ExpProcessItemService expProcessItemService;

    private final ExpProcedureConsumableService expProcedureConsumableService;

    private final ExpProcedureConsumableConverter expProcedureConsumableConverter;
    private final ExpCultivatePlanService cultivatePlanService;



    @Override
    public IPage<ExpProcedureVO> listExpProcedurePages(ExpProcedurePageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpProcedurePO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpProcedurePO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpProcedureVO> result = expProcedureConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpProcedureVO getExpProcedureData(Long id) {
        ExpProcedure sd = this.getById(id);
        ExpProcedureVO expProcedureVO = expProcedureConverter.entity2Vo(sd);
        List<ExpProcessItem> processItemList = expProcessItemService.list(new LambdaQueryWrapper<ExpProcessItem>()
                .eq(ExpProcessItem::getProcedureId, sd.getId())
                .orderByAsc(ExpProcessItem::getSort));
        List<ExpProcessItemVO> resultList = expProcessItemConverter.entity2Vo(processItemList);
        expProcedureVO.setProcessItemList(resultList);

        List<ExpProcedureConsumable> procedureConsumables = expProcedureConsumableService.list(new LambdaQueryWrapper<ExpProcedureConsumable>()
                .eq(ExpProcedureConsumable::getProcedureId,sd.getId())
        );
        List<ExpProcedureConsumableVO> procedureConsumablesVO = expProcedureConsumableConverter.entity3Vo(procedureConsumables);
        expProcedureVO.setProcessConsumableList(procedureConsumablesVO);
        return expProcedureVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    @Transactional
    public boolean saveExpProcedure(ExpProcedureForm form) {
        ExpProcedure expProcedure = expProcedureConverter.form2Entity(form);
        boolean result = this.save(expProcedure);
        if (CollectionUtils.isNotEmpty(form.getProcessItemList())){
            List<ExpProcessItem> processItemList = expProcessItemConverter.form2Entity(form.getProcessItemList());
            for (int i = 0; i < processItemList.size(); i++) {
                ExpProcessItem expProcessItem = processItemList.get(i);
                expProcessItem.setProcedureId(expProcedure.getId());
                expProcessItem.setProcedureName(expProcedure.getName());
                expProcessItem.setSort(i);
                expProcessItem.setNo(RandomLabelUtils.generateUniqueCode());
            }
            expProcessItemService.saveBatch(processItemList);
        }
        if (CollectionUtils.isNotEmpty(form.getProcessConsumableList())){
            List<ExpProcedureConsumable> processConsumableList =expProcedureConsumableConverter.form3Entity(form.getProcessConsumableList());
            for (int i = 0; i < processConsumableList.size(); i++) {
                ExpProcedureConsumable item = processConsumableList.get(i);
                item.setProcedureId(expProcedure.getId());
            }
            expProcedureConsumableService.saveBatch(processConsumableList);
        }
        return result;
    }

    @Override
    @Transactional
    public boolean updateExpProcedure(Long id, ExpProcedureForm form) {
        ExpProcedure expProcedure = expProcedureConverter.form2Entity(form);
        boolean result = this.updateById(expProcedure);

        //删除原来的工序项，重新新增
        expProcessItemService.remove(new LambdaQueryWrapper<ExpProcessItem>()
                .eq(ExpProcessItem::getProcedureId,id));

        expProcedureConsumableService.remove(new LambdaQueryWrapper<ExpProcedureConsumable>()
                .eq(ExpProcedureConsumable::getProcedureId,id));

        if (CollectionUtils.isNotEmpty(form.getProcessItemList())){
            List<ExpProcessItem> processItemList = expProcessItemConverter.form2Entity(form.getProcessItemList());
            for (int i = 0; i < processItemList.size(); i++) {
                ExpProcessItem expProcessItem = processItemList.get(i);
                expProcessItem.setProcedureId(expProcedure.getId());
                expProcessItem.setProcedureName(expProcedure.getName());
                expProcessItem.setSort(i);
                expProcessItem.setNo(RandomLabelUtils.generateUniqueCode());

                TaskDTO taskDTO = new TaskDTO();
                taskDTO.setDeviceId(expProcessItem.getDeviceId());
                taskDTO.setProcedureId(expProcessItem.getProcedureId());
                taskDTO.setStepNumber(expProcessItem.getSort());
                cultivatePlanService.pushDeviceParamsDataWebSocket(taskDTO);
            }
            expProcessItemService.saveBatch(processItemList);
        }

        if (CollectionUtils.isNotEmpty(form.getProcessConsumableList())){
            List<ExpProcedureConsumable> processConsumableList =expProcedureConsumableConverter.form3Entity(form.getProcessConsumableList());
            for (int i = 0; i < processConsumableList.size(); i++) {
                ExpProcedureConsumable item = processConsumableList.get(i);
                item.setProcedureId(expProcedure.getId());
            }
            expProcedureConsumableService.saveBatch(processConsumableList);
        }

        return result;
    }

    @Override
    @Transactional
    public boolean deleteExpProcedure(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        for (Long id : ids) {
            //删除该工序流程的工序步骤
            expProcessItemService.remove(new LambdaQueryWrapper<ExpProcessItem>()
                    .eq(ExpProcessItem::getProcedureId,id));
        }
        return this.removeByIds(ids);
    }

    /**
     * 复制工序流程
     * @param id
     * @return
     */
    @Override
    @Transactional
    public boolean copy(Long id) {
        ExpProcedure expProcedure = this.getById(id);
        String newName = generateUniqueName(expProcedure.getName());
        expProcedure.setName(newName);  // 设置新的名称
        expProcedure.setId(null); //清空id
        expProcedure.setCreateTime(null);
        expProcedure.setCreateBy(null);
        expProcedure.setUpdateTime(null);
        expProcedure.setUpdateBy(null);
        boolean result = this.save(expProcedure);
        //查询出要复制的工序流程的工序步骤
        List<ExpProcessItem> processItemList = expProcessItemService.list(new LambdaQueryWrapper<ExpProcessItem>()
                .eq(ExpProcessItem::getProcedureId, id));
        if (CollectionUtils.isNotEmpty(processItemList)){
            for (ExpProcessItem expProcessItem : processItemList) {
                expProcessItem.setId(null);//清空id
                expProcessItem.setProcedureId(expProcedure.getId()); //设置新的流程id
                expProcessItem.setProcedureName(expProcedure.getName());
            }
            expProcessItemService.saveBatch(processItemList);
        }
        return result;
    }


    /**
     * 生成唯一的工序名称（带递增后缀）
     */
    private String generateUniqueName(String originalName) {
        int copyCount = 1;
        String baseName = originalName;
        String newName = baseName + "_copy" + copyCount;

        // 查找数据库中是否已经存在该名称
        while (this.existsWithName(newName)) {
            copyCount++; // 如果名称已存在，则递增后缀
            newName = baseName + "_copy" + copyCount;
        }

        return newName; // 返回新的唯一名称
    }

    /**
     * 检查数据库中是否已存在指定名称的工序
     */
    private boolean existsWithName(String name) {
        int count = this.count(new LambdaQueryWrapper<ExpProcedure>().eq(ExpProcedure::getName, name));
        return count > 0; // 如果存在该名称，返回true
    }



}
