package com.cohen.control.dev.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 耗材管理 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材管理回传实体")
public class DevConsumableVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 入库单号 */
    @ApiModelProperty("入库单号")
    private String inboundNo;

    /** 耗材编码 */
    @ApiModelProperty("耗材编码")
    private String code;

    /** 耗材基础信息id */
    @ApiModelProperty("耗材基础信息id")
    private Long consumableId;

    /** 耗材名称 */
    @ApiModelProperty("耗材名称")
    private String consumableName;

    /** 入库仓库id */
    @ApiModelProperty("入库仓库id")
    private Long warehouseId;

    /** 入库仓库 */
    @ApiModelProperty("入库仓库")
    private String warehouseName;

    /** 生产日期 */
    @ApiModelProperty("生产日期")
    private Date manufactureDate;

    /** 有效期 */
    @ApiModelProperty("有效期")
    private Date expirationDate;

    /** 单价 */
    @ApiModelProperty("单价")
    private BigDecimal price;

    /** 耗材状态 */
    @ApiModelProperty("耗材状态")
    private Long status;

    /** 耗材分类id */
    @ApiModelProperty("耗材分类id")
    private Long consumableCategoryId;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


    /** 批次号 */
    private String batchNumber;

    /** 所属类型 */

    private String consumableTypeName;

    /** 规格型号 */

    private String model;

    /** 单位 */

    private String unit;

    /** 所属品牌 */

    private String brand;

    /** 生产厂家 */

    private String manufacturer;




}
