package com.cohen.control.dev.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author：shaxiaosong
 * @name：CohenMerchant 审核表  -- 查询对象
 * @Date：2023-04-21 16:50
 */

@Data
@ApiModel("审核表分页视图对象")
public class DevApprovalVO {
    @ApiModelProperty("编号")
    private Long id;

    @ApiModelProperty("流程id = 备用")
    private Long flowId;

    @ApiModelProperty("审批类型：1个人，2机构")
    private Integer approvalProcessType;

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("审批人")
    private String approvarBy;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批原因")
    private String approverReson;

    @ApiModelProperty("审批状态：0待审核，1审核通过，2审核失败")
    private Integer status;

    @ApiModelProperty("创建人Id")
    private Long createId;

    @ApiModelProperty("创建人名称")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人ID")
    private Long updateId;

    @ApiModelProperty("修改人")
    private String updateBy;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
