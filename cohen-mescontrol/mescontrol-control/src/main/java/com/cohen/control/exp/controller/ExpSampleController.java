package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import com.cohen.control.exp.pojo.form.ExpSampleForm;
import com.cohen.control.exp.pojo.query.ExpSamplePageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleVO;
import com.cohen.control.exp.service.ExpSampleService;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 脐带入库单明细（脐带管理）Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "脐带管理接口")
@RestController
@RequestMapping("/api/v1/exp/sample")
@RequiredArgsConstructor
public class ExpSampleController
{
    private final ExpSampleService ExpSampleService;

    @ApiOperation(value = "脐带分页列表")
    @GetMapping("/pages")
        public PageResult listExpSamplePages(ExpSamplePageQuery queryParams){
            IPage<ExpSampleVO> result = ExpSampleService.listExpSamplePages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "脐带表单数据")
    @PreAuthorize("@pms.hasPermission('exp:sample:detail')")
    @GetMapping("/{id}/form")
    public Result getExpSampleDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpSampleVO vo = ExpSampleService.getExpSampleData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增脐带")
    @Log(title = "新增脐带",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:sample:add')")
    @PostMapping
    public Result saveExpSample(
            @RequestBody @Valid ExpSampleForm form
    ) {
        boolean result = ExpSampleService.saveExpSample(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改脐带")
    @Log(title = "修改脐带",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:sample:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpSample(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpSampleForm form
    ) {
        boolean result = ExpSampleService.updateExpSample(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除脐带")
    @Log(title = "删除脐带",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:sample:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpSample(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = ExpSampleService.deleteExpSample(ids);
        return Result.judge(result);
    }


    @ApiOperation(value = "查询计划绑定的样品列表")
    @GetMapping("/getPlanBindSample/{planId}")
    public Result getPlanBindSample(@PathVariable Long planId) {
        List<ExpSampleVO> result = ExpSampleService.getPlanBindSample(planId);
        return Result.success(result);
    }

}
