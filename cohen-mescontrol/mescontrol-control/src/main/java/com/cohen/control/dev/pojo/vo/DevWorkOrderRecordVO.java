package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 工单处理记录 回传实体
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("工单处理记录回传实体")
public class DevWorkOrderRecordVO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 工单id */
    @ApiModelProperty("工单id")
    private Long workOrderId;

    /** 处理人id */
    @ApiModelProperty("处理人id")
    private Long processorId;

    /** 处理人名称 */
    @ApiModelProperty("处理人名称")
    private String processorName;

    /** 处理照片 */
    @ApiModelProperty("处理照片")
    private String processorImage;

    /** 处理结果 */
    @ApiModelProperty("处理结果")
    private String processingResult;

    /** 处理意见 */
    @ApiModelProperty("处理意见")
    private String processingComment;

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    private Date operationTime;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
