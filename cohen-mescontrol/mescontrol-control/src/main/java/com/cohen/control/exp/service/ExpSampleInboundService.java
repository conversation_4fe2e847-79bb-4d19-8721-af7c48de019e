
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpSampleInbound;
import com.cohen.control.exp.pojo.form.ExpSampleInboundForm;
import com.cohen.control.exp.pojo.query.ExpSampleInboundPageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleInboundVO;


/**
 * 脐带入库单Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpSampleInboundService extends IService<ExpSampleInbound> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpSampleInboundVO> listExpSampleInboundPages(ExpSampleInboundPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpSampleInboundVO getExpSampleInboundData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpSampleInbound(ExpSampleInboundForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpSampleInbound(Long id, ExpSampleInboundForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpSampleInbound(String idsStr);
}


