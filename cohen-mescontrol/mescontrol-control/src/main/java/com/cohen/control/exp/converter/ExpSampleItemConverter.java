package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpSampleItem;
import com.cohen.control.exp.pojo.form.ExpSampleItemForm;
import com.cohen.control.exp.pojo.po.ExpSampleItemPO;
import com.cohen.control.exp.pojo.vo.ExpSampleItemVO;

import java.util.List;

/**
 * 脐带入库单明细（脐带管理）转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpSampleItemConverter {

    ExpSampleItemVO po2Vo(ExpSampleItemPO po);
    Page<ExpSampleItemVO> po2Vo(Page<ExpSampleItemPO> po);
    ExpSampleItemVO entity2Vo(ExpSampleItem entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpSampleItem form2Entity(ExpSampleItemForm form);

    List<ExpSampleItem> form2Entity(List<ExpSampleItemForm> sampleItemList);

    List<ExpSampleItemVO> entity2Vo(List<ExpSampleItem> list);
}
