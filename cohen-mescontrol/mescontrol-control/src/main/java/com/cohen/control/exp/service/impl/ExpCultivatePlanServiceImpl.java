package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.schema.ValidateResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.constant.RedisConstants;
import com.cohen.common.enums.PlanStatusEnum;
import com.cohen.common.enums.ProcessPushTypeEnum;
import com.cohen.common.enums.SampleStatusEnum;
import com.cohen.common.enums.TaskStatusEnum;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.web.exception.BizException;
import com.cohen.control.exp.converter.ExpCultivateProcedureConverter;
import com.cohen.control.exp.job.exception.TaskException;
import com.cohen.control.exp.job.domain.SysJob;
import com.cohen.control.exp.job.util.ScheduleUtils;
import com.cohen.control.exp.pojo.dto.TaskDTO;
import com.cohen.control.exp.pojo.entity.*;
import com.cohen.control.exp.pojo.form.ExpCultivateProcedureForm;
import com.cohen.control.exp.pojo.vo.ExpCultivateProcedureVO;
import com.cohen.control.exp.queue.RabbitConstant;
import com.cohen.control.exp.service.*;
import com.cohen.system.notice.api.NoticeFeignClient;
import com.cohen.system.notice.dto.WsQueueMessageDTO;
import com.cohen.system.notice.enums.TerminalTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.redisson.cache.ExpirableValue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCultivatePlanMapper;
import com.cohen.control.exp.pojo.form.ExpCultivatePlanForm;
import com.cohen.control.exp.pojo.po.ExpCultivatePlanPO;
import com.cohen.control.exp.pojo.query.ExpCultivatePlanPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivatePlanVO;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

/**
 * 培养计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCultivatePlanServiceImpl extends ServiceImpl<ExpCultivatePlanMapper, ExpCultivatePlan>implements ExpCultivatePlanService {

    private final com.cohen.control.exp.converter.ExpCultivatePlanConverter ExpCultivatePlanConverter;
    private final ExpCultivateProcedureConverter expCultivateProcedureConverter;
    private final ExpCultivateProcedureService expCultivateProcedureService;
    private final ExpProcessItemService expProcessItemService;
    private final ExpCultivateTaskService expCultivateTaskService;
    private final StringRedisTemplate redisTemplate;
    private final NoticeFeignClient noticeFeignClient;
    private final Scheduler scheduler;
    private final ExpPlanSampleService planSampleService;
    private final ExpSampleService sampleService;
    private final RabbitAdmin rabbitAdmin;


    /**
     * @PostConstruct 注解标记的方法会在依赖注入后自动执行
     * 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库ID和任务组名，否则会导致脏数据）
     * @throws SchedulerException
     */
    @PostConstruct
    public void init() throws SchedulerException, TaskException {
        scheduler.clear();
        List<ExpCultivatePlan> planList = this.list(new LambdaQueryWrapper<ExpCultivatePlan>()
                .eq(ExpCultivatePlan::getStatus, PlanStatusEnum.PENDING_START.getValue()));
        for (ExpCultivatePlan plan : planList) {
            List<ExpCultivateProcedure> list = expCultivateProcedureService.list(new LambdaQueryWrapper<ExpCultivateProcedure>()
                    .eq(ExpCultivateProcedure::getPlanNo, plan.getNo())
                    .orderByAsc(ExpCultivateProcedure::getSort));
            //校验开始时间不能在当前时间之后
            Date startTime = list.get(0).getStartTime();
            Date date = new Date();
            if (startTime.before(date)||startTime.equals(date)){
                SysJob job = createJob(plan, startTime);
                ScheduleUtils.createScheduleJob(scheduler, job);
            }
        }
    }


    @Override
    public IPage<ExpCultivatePlanVO> listExpCultivatePlanPages(ExpCultivatePlanPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCultivatePlanPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCultivatePlanPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCultivatePlanVO> result = ExpCultivatePlanConverter.po2Vo(PoPage);
        for (ExpCultivatePlanVO cultivatePlan : result.getRecords()) {
            //查询是否绑定样品
            int count = planSampleService.count(new LambdaQueryWrapper<ExpPlanSample>()
                    .eq(ExpPlanSample::getPlanId, cultivatePlan.getId())
                    .eq(ExpPlanSample::getPlanNo, cultivatePlan.getNo()));
            cultivatePlan.setSampleBindStatus(count<=0?0:1); //0 未绑定 1.已绑定
        }

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCultivatePlanVO getExpCultivatePlanData(Long id) {
        ExpCultivatePlan sd = this.getById(id);
        ExpCultivatePlanVO ExpCultivatePlanVO = ExpCultivatePlanConverter.entity2Vo(sd);
        List<ExpCultivateProcedure> list = expCultivateProcedureService.list(new LambdaQueryWrapper<ExpCultivateProcedure>()
                .eq(ExpCultivateProcedure::getPlanId, id)
                .eq(ExpCultivateProcedure::getPlanNo, sd.getNo()));
        List<ExpCultivateProcedureVO> voList =expCultivateProcedureConverter.entity2Vo(list);
        ExpCultivatePlanVO.setCultivateProcedureList(voList);
        return ExpCultivatePlanVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    @Transactional
    public boolean saveExpCultivatePlan(ExpCultivatePlanForm form) {
        int count = this.count(new LambdaQueryWrapper<ExpCultivatePlan>()
                .eq(ExpCultivatePlan::getNo, form.getNo()));
        Assert.isTrue(count <= 0, "计划编号已存在");
        hasOverlap(form.getCultivateProcedureList());

        ExpCultivatePlan expCultivatePlan = ExpCultivatePlanConverter.form2Entity(form);
        boolean result = this.save(expCultivatePlan);

        List<ExpCultivateProcedureForm> cultivateProcedureList = form.getCultivateProcedureList();
        if (CollectionUtils.isEmpty(cultivateProcedureList)) {
            return result;
        }
        List<ExpCultivateProcedure> list = expCultivateProcedureConverter.form2Entity(cultivateProcedureList);
        addTask(expCultivatePlan,list);
/*        int stepNumber = 0; // 初始化步骤编号
        AtomicInteger counter = new AtomicInteger(0); // 初始化计数器
        for (int index = 0; index < list.size(); index++) {
            ExpCultivateProcedure expCultivateProcedure = list.get(index);
            expCultivateProcedure.setPlanId(expCultivatePlan.getId());
            expCultivateProcedure.setPlanNo(expCultivatePlan.getNo());
            expCultivateProcedure.setPlanName(expCultivatePlan.getName());
            expCultivateProcedure.setSort(index);

            // 生成任务
            //查询该工序流程配置的工序项
            List<ExpProcessItem> processItemList = expProcessItemService.list(new LambdaQueryWrapper<ExpProcessItem>()
                    .eq(ExpProcessItem::getProcedureId, expCultivateProcedure.getProcessId())
                    .orderByAsc(ExpProcessItem::getSort));
            ArrayList<ExpCultivateTask> taskList = new ArrayList<>();
            for (ExpProcessItem processItem : processItemList) {
                ExpCultivateTask task = new ExpCultivateTask();
                task.setStepNumber(stepNumber);
                task.setPlanNo(expCultivateProcedure.getPlanNo());
                //任务编号生成规则  计划编号+0001，计划编号+0002 以此类推
                String taskNo = task.getPlanNo()+String.format("%03d", counter.incrementAndGet());// 格式化为三位数
                task.setTaskNo(taskNo);
                task.setTaskName(processItem.getName());
                task.setProcedureId(expCultivateProcedure.getProcessId());
                task.setProcessNo(processItem.getNo());
                task.setProcessDescription(processItem.getDescirption());
                task.setDeviceId(processItem.getDeviceId());
                task.setDeviceName(processItem.getDeviceName());
                task.setTaskCreateTime(new Date());
                task.setTaskCreatorName(SecurityUtils.getNickname());
                task.setTaskDuration(processItem.getTaskDuration());
                taskList.add(task);
                stepNumber++; // 更新步骤编号
            }
            expCultivateTaskService.saveBatch(taskList);
        }*/
        //保存工序流程配置信息
        expCultivateProcedureService.saveBatch(list);

        //新增该计划的定时任务
        try {
            SysJob job = createJob(expCultivatePlan, list.get(0).getStartTime());
            ScheduleUtils.createScheduleJob(scheduler, job);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        } catch (TaskException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    public SysJob createJob(ExpCultivatePlan expCultivatePlan,Date startTime){
        SysJob job = new SysJob();
        job.setJobId(expCultivatePlan.getId());
        job.setJobName(expCultivatePlan.getName());
        job.setJobGroup("计划任务");
        job.setConcurrent("1");
        job.setInvokeTarget("expCultivateTaskService.sendTasksToQueue('"+expCultivatePlan.getNo()+"')");
        job.setStatus("0");
        job.setStartTime(startTime);
        return job;
    }

    public void addTask(ExpCultivatePlan expCultivatePlan,List<ExpCultivateProcedure> list){
        int stepNumber = 0; // 初始化步骤编号
        AtomicInteger counter = new AtomicInteger(0); // 初始化计数器
        for (int index = 0; index < list.size(); index++) {
            ExpCultivateProcedure expCultivateProcedure = list.get(index);
            expCultivateProcedure.setPlanId(expCultivatePlan.getId());
            expCultivateProcedure.setPlanNo(expCultivatePlan.getNo());
            expCultivateProcedure.setPlanName(expCultivatePlan.getName());
            expCultivateProcedure.setSort(index);

            //生成任务
            //查询该工序流程配置的工序项
            List<ExpProcessItem> processItemList = expProcessItemService.list(new LambdaQueryWrapper<ExpProcessItem>()
                    .eq(ExpProcessItem::getProcedureId, expCultivateProcedure.getProcessId())
                    .orderByAsc(ExpProcessItem::getSort));
            ArrayList<ExpCultivateTask> taskList = new ArrayList<>();
            for (ExpProcessItem processItem : processItemList) {
                ExpCultivateTask task = new ExpCultivateTask();
                task.setStepNumber(stepNumber);
                task.setPlanNo(expCultivateProcedure.getPlanNo());
                //任务编号生成规则  计划编号+0001，计划编号+0002 以此类推
                String taskNo = task.getPlanNo()+String.format("%03d", counter.incrementAndGet());// 格式化为三位数
                task.setTaskNo(taskNo);
                task.setTaskName(processItem.getName());
                task.setProcedureId(expCultivateProcedure.getProcessId());
                task.setProcessNo(processItem.getNo());
                task.setProcessDescription(processItem.getDescirption());
                task.setDeviceId(processItem.getDeviceId());
                task.setDeviceName(processItem.getDeviceName());
                task.setTaskCreateTime(new Date());
                task.setTaskCreatorName(SecurityUtils.getNickname());
                task.setTaskDuration(processItem.getTaskDuration());
                taskList.add(task);
                stepNumber++; // 更新步骤编号
            }
            expCultivateTaskService.saveBatch(taskList);
        }
    }

    @Override
    @Transactional
    public boolean updateExpCultivatePlan(Long id, ExpCultivatePlanForm form){
        int count = this.count(new LambdaQueryWrapper<ExpCultivatePlan>()
                .eq(ExpCultivatePlan::getNo, form.getNo())
                .ne(ExpCultivatePlan::getId, id));
        if (count>0){throw new BizException("计划编号已存在");}
        ExpCultivatePlan ahDoctor = ExpCultivatePlanConverter.form2Entity(form);
        List<ExpCultivateProcedureForm> procedureList = form.getCultivateProcedureList();
        if (CollectionUtils.isEmpty(procedureList)){ throw new BizException("工序流程配置不能为空");}
        //校验时间
        hasOverlap(procedureList);
        boolean result = this.updateById(ahDoctor);
        //删除计划和工序流程的绑定关系
        expCultivateProcedureService.remove(new LambdaQueryWrapper<ExpCultivateProcedure>()
                .eq(ExpCultivateProcedure::getPlanNo,form.getNo()));
        //删除该计划下的所有任务
        expCultivateTaskService.remove(new LambdaQueryWrapper<ExpCultivateTask>()
                .eq(ExpCultivateTask::getPlanNo,form.getNo()));
        //重新新增
        List<ExpCultivateProcedure> list = expCultivateProcedureConverter.form2Entity(procedureList);
        //保存工序流程配置信息
        expCultivateProcedureService.saveBatch(list);
        addTask(ahDoctor,list);

        //重新给该计划的任务绑定样品
        List<ExpPlanSample> planSampleList = planSampleService.list(new LambdaQueryWrapper<ExpPlanSample>()
                .eq(ExpPlanSample::getPlanId, id)
                .eq(ExpPlanSample::getPlanNo, ahDoctor.getNo()));
        for (ExpPlanSample planSample : planSampleList) {
            //目前计划只绑定一个样品
            expCultivateTaskService.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo,ahDoctor.getNo())
                    .set(ExpCultivateTask::getSampleNo,planSample.getSampleNo()));
        }

        //更新该计划定时任务
        if (result){
            SysJob job = createJob(ahDoctor, procedureList.get(0).getStartTime());
            try {
                updateSchedulerJob(job,job.getJobGroup());
            } catch (SchedulerException e) {
                throw new RuntimeException(e);
            } catch (TaskException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }



    /**
     * 更新任务
     *
     * @param job 任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(SysJob job, String jobGroup) throws SchedulerException, TaskException
    {
        Long jobId = job.getJobId();
        // 判断是否存在
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey))
        {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, job);
    }

    @Override
    @Transactional
    public boolean deleteExpCultivatePlan(String idsStr) throws SchedulerException {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        for (Long id : ids) {
            ExpCultivatePlan cultivatePlan = this.getById(id);
            //删除计划和工序流程的绑定关系
            expCultivateProcedureService.remove(new LambdaQueryWrapper<ExpCultivateProcedure>()
                    .eq(ExpCultivateProcedure::getPlanNo,cultivatePlan.getNo()));
            //删除该计划下的所有任务
            expCultivateTaskService.remove(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo,cultivatePlan.getNo()));
            //删除当前计划定时任务
            SysJob job = createJob(cultivatePlan, null);
            scheduler.deleteJob(ScheduleUtils.getJobKey(job.getJobId(),job.getJobGroup()));
        }
        return this.removeByIds(ids);
    }

    /**
     * 终止培养计划
     * @param form
     * @return
     */
    @Override
    public boolean terminatePlan(ExpCultivatePlanForm form) {
        ExpCultivatePlan cultivatePlan = this.getById(form.getId());

        boolean result = this.update(new LambdaUpdateWrapper<ExpCultivatePlan>()
                .eq(ExpCultivatePlan::getId, form.getId())
                .set(ExpCultivatePlan::getTerminationReason, form.getTerminationReason())
                .set(ExpCultivatePlan::getTerminationAttachment, form.getTerminationAttachment())
                .set(ExpCultivatePlan::getTerminationTime, new Date())
                .set(ExpCultivatePlan::getTerminatedBy, SecurityUtils.getNickname())
                .set(ExpCultivatePlan::getStatus, PlanStatusEnum.TERMINATED.getValue()));

        //清除队列中的消息
        rabbitAdmin.purgeQueue(RabbitConstant.DELAY_QUEUE,false);
        rabbitAdmin.purgeQueue(RabbitConstant.TASK_START_DELAY_QUEUE,false);

        //计划未开始，更新该计划中的所有任务为已终止
//        if (cultivatePlan.getStatus()==PlanStatusEnum.PENDING_START.getValue()){
//            expCultivateTaskService.update(new LambdaUpdateWrapper<ExpCultivateTask>()
//                    .eq(ExpCultivateTask::getPlanNo,cultivatePlan.getNo())
//                    .set(ExpCultivateTask::getTaskStatus,TaskStatusEnum.TERMINATED.getValue())
//                    .set(ExpCultivateTask::getTerminationTime,new Date())
//                    .set(ExpCultivateTask::getTerminationReason,"计划已被终止"));
//        }else
            if (cultivatePlan.getStatus()==PlanStatusEnum.IN_PROGRESS.getValue()){
            //查询当前处于哪个任务
            String step = redisTemplate.opsForValue().get(RedisConstants.PLAN_TASK_STEP + cultivatePlan.getId());
            if (StringUtils.isNotEmpty(step)){
                expCultivateTaskService.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                        .eq(ExpCultivateTask::getPlanNo,cultivatePlan.getNo())
                        .ge(ExpCultivateTask::getStepNumber,step)
                        .set(ExpCultivateTask::getTaskStatus,TaskStatusEnum.TERMINATED.getValue())
                        .set(ExpCultivateTask::getTerminationTime,new Date())
                        .set(ExpCultivateTask::getTerminationReason,"计划已被终止"));
            }
            //删除当前计划的步骤缓存
            redisTemplate.delete(RedisConstants.PLAN_TASK_STEP+cultivatePlan.getId());
        }
        return result;
    }


    /**
     * 校验该计划的时间是否存在冲突
     * @param list
     */
    public void hasOverlap(List<ExpCultivateProcedureForm> list) {
        //按开始时间排序
        Collections.sort(list, Comparator.comparing(ExpCultivateProcedureForm::getStartTime));
        List<ExpCultivateProcedure> procedures = this.baseMapper.selectProcedureList();
        for (int i = 1; i < list.size(); i++) {
            ExpCultivateProcedureForm procedureForm = list.get(i - 1);
            ExpCultivateProcedure expCultivateProcedure = expCultivateProcedureConverter.form2Entity(list.get(i));
            if (isOverlapping(procedureForm, expCultivateProcedure)) {
                throw new BizException("计划中时间存在冲突，请先检查时间");
            }
        }
        for (ExpCultivateProcedureForm procedureForm : list) {
            Date date = new Date();
            if (procedureForm.getEndTime().before(date)||procedureForm.getEndTime().before(date)){
                throw new BizException("不能在当前时间之前");
            }
            for (ExpCultivateProcedure dbProcedure : procedures) {
                if (isOverlapping(procedureForm, dbProcedure)) {
                    throw new BizException("当前计划与(" + dbProcedure.getPlanName() + ")计划时间存在冲突");
                }
            }
        }
    }

    /**
     * 判断两个时间段是否有重叠
     *
     * @param procedure1 第一个记录
     * @param procedure2 第二个记录
     * @return 如果有重叠返回 true，否则返回 false
     */
    public static boolean isOverlapping(ExpCultivateProcedureForm procedure1, ExpCultivateProcedure procedure2) {
        // 确保结束时间和开始时间不重叠
        return procedure1.getStartTime().before(procedure2.getEndTime()) && procedure1.getEndTime().after(procedure2.getStartTime());
    }



    @Override
    @Transactional
    public boolean bindSample(ExpCultivatePlanForm form) {
        List<Long> sampleIdList = form.getSampleIdList();
        if (CollectionUtils.isEmpty(sampleIdList)){
            throw new BizException("样品为空");
        }
        ExpCultivatePlan cultivatePlan = this.getById(form.getId());
        ArrayList<ExpPlanSample> list = new ArrayList<>();
        for (Long sampleId : sampleIdList) {
            ExpPlanSample expPlanSample = new ExpPlanSample();
            expPlanSample.setPlanId(cultivatePlan.getId());
            expPlanSample.setPlanNo(cultivatePlan.getNo());
            expPlanSample.setSampleId(sampleId);
            //查询样品信息
            ExpSample sample = sampleService.getById(sampleId);
            expPlanSample.setSampleNo(sample.getSampleNo());
            list.add(expPlanSample);
            sample.setProductionStatus(SampleStatusEnum.PENDING_START.getValue());
            sample.setPlanId(cultivatePlan.getId());
            sample.setPlanName(cultivatePlan.getName());
            sampleService.updateById(sample);

            //目前计划只绑定一个样品
            expCultivateTaskService.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo,cultivatePlan.getNo())
                    .set(ExpCultivateTask::getSampleNo,sample.getSampleNo()));
        }
        boolean result = planSampleService.saveBatch(list);
        return result;
    }


    @Override
    public Map<String, Object> getPlanAndDeviceParam(Long processId) {
        Map<String, Object> resultMap = new HashMap<>();
        //查询计划信息
//        ExpCultivatePlan cultivatePlan = this.getById(id);
        ExpCultivatePlan cultivatePlan = getNearestPlanToCurrentProcess(processId);
        if (cultivatePlan == null){
            throw new BizException("未查询到计划");
        }

        //查询当前计划绑定工序的设备参数
        List<Map<String,String>> deviceMap=this.baseMapper.getDeviceParams(processId);

        resultMap.put("plan",cultivatePlan);
        if (cultivatePlan.getStatus()==PlanStatusEnum.COMPLETED.getValue()){
            ExpCultivateTask task = expCultivateTaskService.getOne(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo, cultivatePlan.getNo())
                    .eq(ExpCultivateTask::getProcedureId, processId)
                    .eq(ExpCultivateTask::getTaskStatus, TaskStatusEnum.COMPLETED.getValue())
                    .orderByDesc(ExpCultivateTask::getStepNumber)
                    .last("LIMIT  1"));

            resultMap.put("taskStep", task.getStepNumber()+1);
        }else if (cultivatePlan.getStatus()==PlanStatusEnum.PENDING_START.getValue()||
        cultivatePlan.getStatus()==PlanStatusEnum.IN_PROGRESS.getValue()){
            //从缓存中获取当前处于计划中的那个任务
            String step = redisTemplate.opsForValue().get(RedisConstants.PLAN_TASK_STEP+cultivatePlan.getId());
            resultMap.put("taskStep", StringUtils.isNotEmpty(step)?step:0);
        }else { //计划终止
            ExpCultivateTask task = expCultivateTaskService.getOne(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo, cultivatePlan.getNo())
                    .eq(ExpCultivateTask::getProcedureId, processId)
                    .eq(ExpCultivateTask::getTaskStatus, TaskStatusEnum.TERMINATED.getValue())
                    .orderByAsc(ExpCultivateTask::getStepNumber)
                    .last("LIMIT  1"));
            resultMap.put("taskStep", task.getStepNumber());
        }
        resultMap.put("device",deviceMap);
        return resultMap;
    }

    /**
     * 查询当前工艺最近的计划
     * @param processId
     * @return
     */
    public ExpCultivatePlan getNearestPlanToCurrentProcess(Long processId){
        ExpCultivatePlan plan =  this.baseMapper.getNearestPlanToCurrentProcess(processId);
        return plan;
    }


    /**
     * 推送设备信息到webSocket
     */
    @Override
    public void  pushDeviceParamsDataWebSocket(TaskDTO taskDTO){
        //查询工序中设备参数
   /*     Map<String,Object> deviceMap = expProcessItemService.getProcessDeviceParams(taskDTO.getProcedureId(),taskDTO.getDeviceId(),taskDTO.getStepNumber());
        //推送当前任务的消息到websocket中
        HashMap<String,Object> message = new HashMap<>();
        message.put("content",deviceMap);
        message.put("type" , ProcessPushTypeEnum.DEVICE_INFO.getValue());
        WsQueueMessageDTO dto = new WsQueueMessageDTO();
        dto.setDestination(String.format("/queue/process/plan"));
        dto.setBody(JSON.toJSONString(message));
        noticeFeignClient.send(TerminalTypeEnum.WS.getValue(),JSON.toJSONString(dto));*/
    }

    @Override
    public void  pushStepWebSocket(TaskDTO taskDTO){
        //推送当前任务的消息到websocket中
   /*     HashMap<String,Object> message = new HashMap<>();
        message.put("content",taskDTO.getStepNumber());
        message.put("type" , ProcessPushTypeEnum.PROCESS_STEP.getValue());
        WsQueueMessageDTO dto = new WsQueueMessageDTO();
        dto.setDestination(String.format("/queue/process/plan"));
        dto.setBody(JSON.toJSONString(message));
        noticeFeignClient.send(TerminalTypeEnum.WS.getValue(),JSON.toJSONString(dto));*/
    }

    @Override
    public void  pushPlanWebSocket(TaskDTO taskDTO){
        ExpCultivatePlan cultivatePlan = this.getNearestPlanToCurrentProcess(taskDTO.getProcedureId());
        //推送当前任务的消息到websocket中
        HashMap<String,Object> message = new HashMap<>();
        message.put("content",cultivatePlan);
        message.put("type" , ProcessPushTypeEnum.PLAN_INFO.getValue());
        WsQueueMessageDTO dto = new WsQueueMessageDTO();
        dto.setDestination(String.format("/queue/process/plan"));
        dto.setBody(JSON.toJSONString(message));
        noticeFeignClient.send(TerminalTypeEnum.WS.getValue(),JSON.toJSONString(dto));
    }

}
