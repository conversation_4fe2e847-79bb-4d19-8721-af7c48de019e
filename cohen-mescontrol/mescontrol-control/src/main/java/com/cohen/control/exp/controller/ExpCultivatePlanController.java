package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCultivatePlanForm;
import com.cohen.control.exp.pojo.query.ExpCultivatePlanPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivatePlanVO;
import com.cohen.control.exp.service.ExpCultivatePlanService;

import java.util.Map;


/**
 * 培养计划Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "培养计划接口")
@RestController
@RequestMapping("/api/v1/exp/plan")
@RequiredArgsConstructor
public class ExpCultivatePlanController
{
    private final ExpCultivatePlanService expCultivatePlanService;

    @ApiOperation(value = "培养计划分页列表")
    @GetMapping("/pages")
        public PageResult listExpCultivatePlanPages(ExpCultivatePlanPageQuery queryParams){
            IPage<ExpCultivatePlanVO> result = expCultivatePlanService.listExpCultivatePlanPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "培养计划表单数据")
    @PreAuthorize("@pms.hasPermission('exp:plan:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCultivatePlanDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCultivatePlanVO vo = expCultivatePlanService.getExpCultivatePlanData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增培养计划")
    @Log(title = "新增培养计划",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:plan:add')")
    @PostMapping
    public Result saveExpCultivatePlan(
            @RequestBody @Valid ExpCultivatePlanForm form
    ) {
        boolean result = expCultivatePlanService.saveExpCultivatePlan(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改培养计划")
    @Log(title = "修改培养计划",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:plan:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCultivatePlan(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCultivatePlanForm form
    ) {
        boolean result = expCultivatePlanService.updateExpCultivatePlan(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除培养计划")
    @Log(title = "删除培养计划",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:plan:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCultivatePlan(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) throws SchedulerException {
        boolean result = expCultivatePlanService.deleteExpCultivatePlan(ids);
        return Result.judge(result);
    }


    @ApiOperation(value = "终止培养计划")
    @Log(title = "终止培养计划",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:plan:terminate')")
    @PutMapping("/terminatePlan")
    public Result terminatePlan(@RequestBody ExpCultivatePlanForm form){
        boolean result = expCultivatePlanService.terminatePlan(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "培养计划绑定样品")
    @Log(title = "培养计划绑定样品",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:plan:bindSample')")
    @PostMapping("/bindSample")
    public Result bindSample(@RequestBody ExpCultivatePlanForm form){
        boolean result = expCultivatePlanService.bindSample(form);
        return Result.judge(result);
    }


    @ApiOperation(value = "查询计划和设备参数")
    @GetMapping("/initPlanProcess/{processId}")
    public Result initPlanProcess(@PathVariable Long processId){
        Map<String,Object> result = expCultivatePlanService.getPlanAndDeviceParam(processId);
        return Result.success(result);
    }
}
