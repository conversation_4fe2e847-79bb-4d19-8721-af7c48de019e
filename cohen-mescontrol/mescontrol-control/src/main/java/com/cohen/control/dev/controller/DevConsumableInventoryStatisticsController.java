package com.cohen.control.dev.controller;

import com.cohen.control.dev.pojo.form.DevConsumableInventoryStatisticsForm;
import com.cohen.control.dev.pojo.vo.DevConsumableInventoryS1VO;
import com.cohen.control.dev.pojo.vo.DevConsumableInventoryStatisticsVO;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;

import com.cohen.control.dev.pojo.query.DevConsumableInventoryStatisticsPageQuery;
import com.cohen.control.dev.service.DevConsumableInventoryStatisticsService;


/**
 * 耗材库存统计Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "耗材库存统计接口")
@RestController
@RequestMapping("/api/v1/dev/statistics")
@RequiredArgsConstructor
public class DevConsumableInventoryStatisticsController
{
    private final DevConsumableInventoryStatisticsService devConsumableInventoryStatisticsService;

    @ApiOperation(value = "耗材库存统计分页列表")
    @GetMapping("/pages")
        public PageResult listDevConsumableInventoryStatisticsPages(DevConsumableInventoryStatisticsPageQuery queryParams){
            IPage<DevConsumableInventoryStatisticsVO> result = devConsumableInventoryStatisticsService.listDevConsumableInventoryStatisticsPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "库存管理")
    @GetMapping("/inventoryPages")
    public PageResult inventoryPages(DevConsumableInventoryStatisticsPageQuery queryParams){
        IPage<DevConsumableInventoryS1VO> result = devConsumableInventoryStatisticsService.inventoryPages(queryParams);
        return PageResult.success(result);
    }


    @ApiOperation(value = "耗材库存统计表单数据")
    @PreAuthorize("@pms.hasPermission('dev:statistics:detail')")
    @GetMapping("/{id}/form")
    public Result getDevConsumableInventoryStatisticsDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        DevConsumableInventoryStatisticsVO vo = devConsumableInventoryStatisticsService.getDevConsumableInventoryStatisticsData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增耗材库存统计")
    @Log(title = "新增耗材库存统计",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('dev:statistics:add')")
    @PostMapping
    public Result saveDevConsumableInventoryStatistics(
            @RequestBody @Valid DevConsumableInventoryStatisticsForm form
    ) {
        boolean result = devConsumableInventoryStatisticsService.saveDevConsumableInventoryStatistics(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改耗材库存统计")
    @Log(title = "修改耗材库存统计",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('dev:statistics:update')")
    @PutMapping(value = "/{id}")
    public Result updateDevConsumableInventoryStatistics(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated DevConsumableInventoryStatisticsForm form
    ) {
        boolean result = devConsumableInventoryStatisticsService.updateDevConsumableInventoryStatistics(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除耗材库存统计")
    @Log(title = "删除耗材库存统计",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('dev:statistics:del')")
    @DeleteMapping("/{ids}")
    public Result deleteDevConsumableInventoryStatistics(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = devConsumableInventoryStatisticsService.deleteDevConsumableInventoryStatistics(ids);
        return Result.judge(result);
    }


}
