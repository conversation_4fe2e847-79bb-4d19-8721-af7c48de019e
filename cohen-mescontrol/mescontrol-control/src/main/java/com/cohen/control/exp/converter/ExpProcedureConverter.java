package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpProcedure;
import com.cohen.control.exp.pojo.form.ExpProcedureForm;
import com.cohen.control.exp.pojo.po.ExpProcedurePO;
import com.cohen.control.exp.pojo.vo.ExpProcedureVO;

/**
 * 工序流程转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpProcedureConverter {

    ExpProcedureVO po2Vo(ExpProcedurePO po);
    Page<ExpProcedureVO> po2Vo(Page<ExpProcedurePO> po);
    ExpProcedureVO entity2Vo(ExpProcedure entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpProcedure form2Entity(ExpProcedureForm form);
}
