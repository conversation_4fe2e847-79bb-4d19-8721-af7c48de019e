package com.cohen.control.dev.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-11-14
 */

@ApiModel("审核日志日志")
@Data
public class ApproveLogVO {

    /** id */
    @ApiModelProperty("id")
    private String id;

    /** 类型 */
    @ApiModelProperty("类型")
    private String type;

    /** 工单标题 */
    @ApiModelProperty("工单标题")
    private String title;

    /** 优先级 */
    @ApiModelProperty("优先级")
    private String priority;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /** 工艺id */
    @ApiModelProperty("工艺id")
    private Long processId;

    /** 工艺名称 */
    @ApiModelProperty("工艺名称")
    private String processName;


    /** 要求完成日期 */
    @ApiModelProperty("要求完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    /** 审批人 */
    @ApiModelProperty("审批人")
    private String approvalBy;

    /** 审批时间 */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审批结果 */
    @ApiModelProperty("审批结果：0待审核，1审核通过，2审核失败")
    private Integer approvalStatus;

    /** 审批原因 */
    @ApiModelProperty("审批原因")
    private String  approverReson;//审批原因
}
