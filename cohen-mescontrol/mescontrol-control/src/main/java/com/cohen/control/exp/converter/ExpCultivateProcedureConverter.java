package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateProcedure;
import com.cohen.control.exp.pojo.form.ExpCultivateProcedureForm;
import com.cohen.control.exp.pojo.po.ExpCultivateProcedurePO;
import com.cohen.control.exp.pojo.vo.ExpCultivateProcedureVO;

import java.util.List;

/**
 * 培养计划绑定工序流程转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCultivateProcedureConverter {

    ExpCultivateProcedureVO po2Vo(ExpCultivateProcedurePO po);
    Page<ExpCultivateProcedureVO> po2Vo(Page<ExpCultivateProcedurePO> po);
    ExpCultivateProcedureVO entity2Vo(ExpCultivateProcedure entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCultivateProcedure form2Entity(ExpCultivateProcedureForm form);

    List<ExpCultivateProcedure> form2Entity(List<ExpCultivateProcedureForm> list);

    List<ExpCultivateProcedureVO> entity2Vo(List<ExpCultivateProcedure> list);
}
