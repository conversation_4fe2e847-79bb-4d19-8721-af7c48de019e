package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备耗材 回传实体
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("设备耗材回传实体")
public class DevDeviceConsumableVO {

    private static final long serialVersionUID = 1L;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 耗材id */
    @ApiModelProperty("耗材id")
    private Long consumableId;

    /** 消耗数量 */
    @ApiModelProperty("消耗数量")
    private Integer quantity;

    /** 消耗数量 */
    @ApiModelProperty("消耗数量")
    private String consumableName;

    /** 耗材类型 */
    @ApiModelProperty("耗材类型")
    private String consumableTypeName;

    /** 规格 */
    @ApiModelProperty("规格")
    private String model;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

}
