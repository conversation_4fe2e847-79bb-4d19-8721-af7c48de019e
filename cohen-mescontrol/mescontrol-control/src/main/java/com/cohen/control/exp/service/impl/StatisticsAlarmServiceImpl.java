package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.exp.converter.StatisticsAlarmConverter;
import com.cohen.control.exp.mapper.StatisticsAlarmMapper;
import com.cohen.control.exp.pojo.entity.StatisticsAlarm;
import com.cohen.control.exp.pojo.po.StatisticsAlarmPO;
import com.cohen.control.exp.pojo.query.StatisticsAlarmPageQuery;
import com.cohen.control.exp.pojo.vo.StatisticsAlarmVO;
import com.cohen.control.exp.pojo.vo.IndexCountAllVO;
import com.cohen.control.exp.service.StatisticsAlarmService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 脐带管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class StatisticsAlarmServiceImpl extends ServiceImpl<StatisticsAlarmMapper, StatisticsAlarm>implements StatisticsAlarmService {

    private final StatisticsAlarmConverter StatisticsAlarmConverter;


    @Override
    public IPage<StatisticsAlarmVO> listStatisticsAlarmPages(StatisticsAlarmPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<StatisticsAlarmPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<StatisticsAlarmPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<StatisticsAlarmVO> result = StatisticsAlarmConverter.po2Vo(PoPage);

        return result;
    }


    @Override
    public IndexCountAllVO indexCount() {
        return null;
    }


    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public StatisticsAlarmVO getStatisticsAlarmData(Long id) {
        StatisticsAlarm sd = this.getById(id);
        StatisticsAlarmVO StatisticsAlarmVO = StatisticsAlarmConverter.entity2Vo(sd);
        return StatisticsAlarmVO;
    }


    @Override
    public boolean deleteStatisticsAlarm(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
