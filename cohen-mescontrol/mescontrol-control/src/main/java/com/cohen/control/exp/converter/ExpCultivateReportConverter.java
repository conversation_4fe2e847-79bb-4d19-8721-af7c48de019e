package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateReport;
import com.cohen.control.exp.pojo.form.ExpCultivateReportForm;
import com.cohen.control.exp.pojo.po.ExpCultivateReportPO;
import com.cohen.control.exp.pojo.vo.ExpCultivateReportVO;

/**
 * 培养报告转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCultivateReportConverter {

    ExpCultivateReportVO po2Vo(ExpCultivateReportPO po);
    Page<ExpCultivateReportVO> po2Vo(Page<ExpCultivateReportPO> po);
    ExpCultivateReportVO entity2Vo(ExpCultivateReport entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCultivateReport form2Entity(ExpCultivateReportForm form);
}
