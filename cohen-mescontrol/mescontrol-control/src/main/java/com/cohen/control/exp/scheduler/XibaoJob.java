package com.cohen.control.exp.scheduler;

import com.cohen.control.exp.mapper.StatisticsCellMapper;
import com.cohen.control.exp.mapper.StatisticsQualifiedCellMapper;
import com.cohen.control.exp.pojo.entity.StatisticsCell;
import com.cohen.control.exp.pojo.entity.StatisticsQualifiedCell;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;


@Slf4j
@Component
@RequiredArgsConstructor
public class XibaoJob {


    private final StatisticsCellMapper statisticsCellMapper;

    private final StatisticsQualifiedCellMapper qualifiedCellMapper;

    private static final long MIN_CELL_QUANTITY = 20000L;
    private static final int CELL_QUANTITY_RANGE = 80001;
    private static final double MIN_TEMP = -37.5;
    private static final double MAX_TEMP = 37.5;


    @Scheduled(cron = "0 0/5 * * * ?")
    public void executeTask() {
        try {
            long cellQuantity = MIN_CELL_QUANTITY + ThreadLocalRandom.current().nextInt(CELL_QUANTITY_RANGE); // 20000 到 100000 之间
            BigDecimal temperature = new BigDecimal(String.valueOf(MIN_TEMP + (MAX_TEMP - MIN_TEMP) * ThreadLocalRandom.current().nextDouble()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            StatisticsCell cell = new StatisticsCell();
            cell.setCellQuantity(cellQuantity);
            cell.setTemperature(temperature);
            cell.setDeviceId(2L);
            cell.setDeviceName("控湿恒温培养箱");
            cell.setDataPushTime(Date.from(Instant.now()));

            statisticsCellMapper.insert(cell);
            System.out.println("定时任务执行成功，CellQuantity=" + cellQuantity + ", Temperature=" + temperature);

        } catch (Exception e) {
            System.err.println("定时任务执行失败: " + e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 12 * * ?")
    public void executehegeTask() {
        try {
            BigDecimal temperature = new BigDecimal(String.valueOf(MIN_TEMP + (MAX_TEMP - MIN_TEMP) * ThreadLocalRandom.current().nextDouble()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            Random random = new Random();
            StatisticsQualifiedCell qualifiedCell1 = new StatisticsQualifiedCell();
            qualifiedCell1.setCellQuantity(10000 + random.nextInt(90000));
            qualifiedCell1.setIsQualified(1);
            qualifiedCell1.setType(1);
            qualifiedCell1.setDeviceId(2L);
            qualifiedCell1.setDeviceName("控湿恒温培养箱");
            qualifiedCell1.setCheckDate(new Date());
            qualifiedCell1.setTemperature(temperature);
            qualifiedCellMapper.insert(qualifiedCell1);

            StatisticsQualifiedCell qualifiedCell2 = new StatisticsQualifiedCell();
            qualifiedCell2.setCellQuantity(10000 + random.nextInt(90000));
            qualifiedCell2.setIsQualified(2);
            qualifiedCell2.setType(1);
            qualifiedCell2.setDeviceId(2L);
            qualifiedCell2.setDeviceName("控湿恒温培养箱");
            qualifiedCell2.setTemperature(temperature);
            qualifiedCell2.setCheckDate(new Date());
            qualifiedCellMapper.insert(qualifiedCell2);

            StatisticsQualifiedCell qualifiedCell3 = new StatisticsQualifiedCell();
            qualifiedCell3.setCellQuantity(10 + random.nextInt(90));
            qualifiedCell3.setIsQualified(1);
            qualifiedCell3.setType(2);
            qualifiedCell3.setDeviceId(2L);
            qualifiedCell3.setDeviceName("控湿恒温培养箱");
            qualifiedCell3.setTemperature(temperature);
            qualifiedCell3.setCheckDate(new Date());
            qualifiedCellMapper.insert(qualifiedCell3);

            StatisticsQualifiedCell qualifiedCell4 = new StatisticsQualifiedCell();
            qualifiedCell4.setCellQuantity(10 + random.nextInt(90));
            qualifiedCell4.setIsQualified(2);
            qualifiedCell4.setType(2);
            qualifiedCell4.setDeviceId(2L);
            qualifiedCell4.setDeviceName("控湿恒温培养箱");
            qualifiedCell4.setTemperature(temperature);
            qualifiedCell4.setCheckDate(new Date());
            qualifiedCellMapper.insert(qualifiedCell4);

        } catch (Exception e) {
            System.err.println("定时任务执行失败: " + e.getMessage());
        }
    }




}
