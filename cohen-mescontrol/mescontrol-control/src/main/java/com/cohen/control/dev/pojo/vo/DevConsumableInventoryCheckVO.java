package com.cohen.control.dev.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 耗材盘点 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材盘点回传实体")
public class DevConsumableInventoryCheckVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 盘点编号 */
    @ApiModelProperty("盘点编号")
    private String no;

    /** 仓库编号 */
    @ApiModelProperty("仓库编号")
    private String warehouseId;

    /** 仓库名称 */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /** 所属区域 */
    @ApiModelProperty("所属区域")
    private String warehouseRegion;

    /** 详细地址 */
    @ApiModelProperty("详细地址")
    private String warehouseAddress;

    /** 联系人 */
    @ApiModelProperty("联系人")
    private String warehouseContact;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String warehouseContactPhone;

    /** 仓库存量 */
    @ApiModelProperty("仓库存量")
    private String inventoryNum;

    /** 当前库存 */
    @ApiModelProperty("当前库存")
    private Integer currentNum;

    /** 实际库存 */
    @ApiModelProperty("实际库存")
    private Integer actualNum;

    /** 差异库存 */
    @ApiModelProperty("差异库存")
    private Integer differenceNum;

    /** 盘点人 */
    @ApiModelProperty("盘点人")
    private String inventoryPersonnel;

    /** 盘点时间 */
    @ApiModelProperty("盘点时间")
    private Date inventoryTime;

    /** 盘点状态 */
    @ApiModelProperty("盘点状态")
    private Integer inventoryStatus;

    /** 核实人 */
    @ApiModelProperty("核实人")
    private String verifyBy;

    /** 核实时间 */
    @ApiModelProperty("核实时间")
    private Date verifyTime;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
