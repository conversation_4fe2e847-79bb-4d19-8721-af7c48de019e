package com.cohen.control.exp.queue;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cohen.common.constant.RedisConstants;
import com.cohen.common.enums.PlanStatusEnum;
import com.cohen.common.enums.TaskStatusEnum;
import com.cohen.common.utils.GenerateNo;
import com.cohen.common.utils.RandomLabelUtils;
import com.cohen.control.dev.service.DevConsumableUseRecordService;
import com.cohen.control.exp.pojo.dto.TaskDTO;
import com.cohen.control.exp.pojo.entity.ExpCellInventory;
import com.cohen.control.exp.pojo.entity.ExpCellRecord;
import com.cohen.control.exp.pojo.entity.ExpCultivatePlan;
import com.cohen.control.exp.pojo.entity.ExpCultivateTask;
import com.cohen.control.exp.service.ExpCellInventoryService;
import com.cohen.control.exp.service.ExpCellRecordService;
import com.cohen.control.exp.service.ExpCultivatePlanService;
import com.cohen.control.exp.service.ExpCultivateTaskService;
import com.cohen.system.notice.api.NoticeFeignClient;
import com.cohen.system.notice.dto.WsQueueMessageDTO;
import com.cohen.system.notice.enums.TerminalTypeEnum;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @create 2024-12-13
 * 消费者
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskConsumerListener {

    private final DevConsumableUseRecordService consumableUseRecordService;
    private final ExpCultivateTaskService cultivateTaskService;
    private final ExpCultivatePlanService cultivatePlanService;
    private final StringRedisTemplate redisTemplate;
    private final NoticeFeignClient noticeFeignClient;
    private final ExpCellInventoryService cellInventoryService;
    private final ExpCellRecordService cellRecordService;

    // 用枚举代替字符串常量
    public enum TaskType {
        START, END
    }


    /**
     * 任务结束监听
     *
     * @param message
     */
    @RabbitListener(queues = {RabbitConstant.DEAD_QUEUE_NAME},ackMode = "MANUAL")
    public void taskEndMessage(Message  message, Channel channel) throws IOException {
        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String msg = new String(message.getBody());
        msg = msg.substring(1, msg.length() - 1).replace("\\\"", "\"");

        log.info("task结束消费者接收到任务消息[{}]", msg);
        System.out.println("task结束消费者接收到任务消息=====>" + msg);

        TaskDTO taskDTO = JSON.parseObject(msg, TaskDTO.class);
        try {
            updatePlanAndTask(taskDTO, TaskType.END);
            //添加该任务的单个设备的耗材消耗记录
            consumableUseRecordService.saveConsumableRecord(taskDTO);
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
        }catch (Exception e){
            e.printStackTrace();
            channel.basicAck(deliveryTag, false); //本队列放行
        }

    }


    /**
     * 任务开始监听
     *
     * @param message
     */
    @RabbitListener(queues = {RabbitConstant.TASK_START_DEAD_QUEUE_NAME},ackMode = "MANUAL")
    public void taskStartMessage(Message message,Channel channel) throws IOException {

        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String msg = new String(message.getBody());
        msg = msg.substring(1, msg.length() - 1).replace("\\\"", "\"");

        log.info("task开始消费者接收到任务消息[{}]", msg);
        System.out.println("task开始消费者接收到任务消息=====>" + msg);

        TaskDTO taskDTO = JSON.parseObject(msg, TaskDTO.class);

        try {
            //推送数据到webSocket
            cultivatePlanService.pushDeviceParamsDataWebSocket(taskDTO);
            cultivatePlanService.pushStepWebSocket(taskDTO);
            cultivatePlanService.pushPlanWebSocket(taskDTO);

            updatePlanAndTask(taskDTO, TaskType.START);
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
        }catch (Exception e){
            e.printStackTrace();
            channel.basicAck(deliveryTag, false); //本队列放行
        }

    }

    /**
     * 更新计划 任务信息
     *
     * @param taskDTO
     * @param type
     */
    @Transactional
    public void updatePlanAndTask(TaskDTO taskDTO, TaskType type) {
        //查询计划
        ExpCultivatePlan cultivatePlan = cultivatePlanService.getById(taskDTO.getPlanId());

        ExpCultivateTask task = new ExpCultivateTask();
        task.setId(taskDTO.getTaskId());
        task.setTaskNo(taskDTO.getTaskNo());
        task.setStepNumber(taskDTO.getStepNumber());
        switch (type) {
            case START:
                updateTaskStart(task, cultivatePlan);
                break;
            case END:
                updateTaskEnd(task, taskDTO, cultivatePlan);
                break;
            default:
                break;
        }
        //更新计划
        cultivatePlanService.updateById(cultivatePlan);
    }

    /**
     * 更新任务开始状态
     *
     * @param task
     * @param cultivatePlan
     */
    private void updateTaskStart(ExpCultivateTask task, ExpCultivatePlan cultivatePlan) {
        task.setStartTime(new Date());
        task.setTaskStatus(TaskStatusEnum.PENDING_COMPLETION.getValue());
        cultivateTaskService.updateById(task);

        if (cultivatePlan.getStatus() != PlanStatusEnum.IN_PROGRESS.getValue()) {
            cultivatePlan.setStatus(PlanStatusEnum.IN_PROGRESS.getValue());
            cultivatePlan.setStartTime(new Date());
        }
        //更新缓存中任务步骤
        redisTemplate.opsForValue().set(RedisConstants.PLAN_TASK_STEP+cultivatePlan.getId(),String.valueOf(task.getStepNumber()));
    }

    /**
     * 更新任务结束状态
     *
     * @param task
     * @param taskDTO
     * @param cultivatePlan
     */
    private void updateTaskEnd(ExpCultivateTask task, TaskDTO taskDTO, ExpCultivatePlan cultivatePlan) {
        ExpCultivateTask cultivateTask = cultivateTaskService.getById(taskDTO.getTaskId());
        Date date = new Date();
        if (cultivateTask != null) {
            task.setCompletionTime(date);
            //计算完成时长
            long duration = (task.getCompletionTime().getTime() - cultivateTask.getStartTime().getTime()) / 1000;
            task.setCompletionDuration(String.valueOf(duration));
            task.setTaskStatus(TaskStatusEnum.COMPLETED.getValue());
            cultivateTaskService.updateById(task);

            // 查询当前计划中的任务是否全部完成
            int count = cultivateTaskService.count(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo, taskDTO.getPlanNo())
                    .ne(ExpCultivateTask::getTaskStatus, TaskStatusEnum.COMPLETED.getValue()));
            if (count <= 0) {
                cultivatePlan.setEndTime(date);
                cultivatePlan.setStatus(PlanStatusEnum.COMPLETED.getValue());
                //计划执行完成，删除任务步骤
                redisTemplate.delete(RedisConstants.PLAN_TASK_STEP+taskDTO.getPlanId());
                //生成细胞库存和细胞记录
                cultivateTask.setCompletionTime(date);
                ExpCellRecord cell = createCell(cultivateTask);
                cultivateTaskService.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                        .eq(ExpCultivateTask::getId,taskDTO.getTaskId())
                        .set(ExpCultivateTask::getCellNo,cell.getCellNo()));
            }

            //查询当前工序的工序项是否全部完成
            int number = cultivateTaskService.count(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getPlanNo, taskDTO.getPlanNo())
                    .eq(ExpCultivateTask::getProcedureId,taskDTO.getProcedureId())
                    .ne(ExpCultivateTask::getTaskStatus, TaskStatusEnum.COMPLETED.getValue()));
            if (number<=0){
                //新增工序的耗材消耗记录
                consumableUseRecordService.addProcessConsumableRecord(taskDTO);
            }
        }
    }



    public ExpCellRecord  createCell(ExpCultivateTask task){
        ExpCellRecord cellRecord = new ExpCellRecord();
        cellRecord.setCellNo(GenerateNo.generateUniqueCode());
        cellRecord.setSampleNo(task.getSampleNo());
        cellRecord.setProcessNode(task.getTaskName());
        cellRecord.setDeviceId(task.getDeviceId());
        cellRecord.setDeviceName(task.getDeviceName());
        cellRecord.setOperator(task.getCompleterName());
        cellRecord.setTaskCreationTime(task.getTaskCreateTime());
        cellRecord.setTaskCompletionTime(task.getTaskCompletionTime());
        cellRecord.setTaskResult(task.getCompletionDuration());
        cellRecordService.save(cellRecord);



        ExpCellInventory cellInventory = new ExpCellInventory();
        cellInventory.setSampleNo(task.getSampleNo());
        cellInventory.setCellNo(cellRecord.getCellNo());
        cellInventory.setStorageTime(new Date());
        cellInventory.setProductionStartTime(task.getStartTime());
        cellInventory.setProductionEndTime(task.getCompletionTime());
        cellInventory.setProductionDuration(String.valueOf(new Random().nextInt(10) + 1));
        cellInventory.setStorageLocation("一号工作细胞库");
        cellInventory.setCellQuantity(new Random().nextInt(90000) + 1000);
        cellInventory.setOutboundStatus(1);
        cellInventoryService.save(cellInventory);
        return  cellRecord;
    }


}
