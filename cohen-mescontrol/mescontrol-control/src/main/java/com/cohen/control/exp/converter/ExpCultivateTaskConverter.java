package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateTask;
import com.cohen.control.exp.pojo.form.ExpCultivateTaskForm;
import com.cohen.control.exp.pojo.po.ExpCultivateTaskPO;
import com.cohen.control.exp.pojo.vo.ExpCultivateTaskVO;

/**
 * 培养任务转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCultivateTaskConverter {

    ExpCultivateTaskVO po2Vo(ExpCultivateTaskPO po);
    Page<ExpCultivateTaskVO> po2Vo(Page<ExpCultivateTaskPO> po);
    ExpCultivateTaskVO entity2Vo(ExpCultivateTask entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCultivateTask form2Entity(ExpCultivateTaskForm form);
}
