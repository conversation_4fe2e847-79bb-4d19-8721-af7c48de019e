package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 保养规则关联设备 回传实体
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("保养规则关联设备回传实体")
public class DevDeviceMaintenanceRulesVO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 保养规则id */
    @ApiModelProperty("保养规则id")
    private Long ruleId;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /** 设备类型id */
    @ApiModelProperty("设备类型id")
    private Long deviceTypeId;

    /** 设备类型 */
    @ApiModelProperty("设备类型")
    private String deviceTypeName;

    /** 设备状态 */
    @ApiModelProperty("设备状态")
    private Integer status;

    /** 区域 */
    @ApiModelProperty("区域")
    private String region;

    /** 污水厂id */
    @ApiModelProperty("污水厂id")
    private Long stationId;

    /** 污水厂名称 */
    @ApiModelProperty("污水厂名称")
    private String stationName;

    /** 详细地址 */
    @ApiModelProperty("详细地址")
    private String address;



}
