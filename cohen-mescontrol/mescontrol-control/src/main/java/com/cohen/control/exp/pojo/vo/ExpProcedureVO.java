package com.cohen.control.exp.pojo.vo;

import com.cohen.control.exp.pojo.form.ExpProcedureConsumableForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 工序流程 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("工序流程回传实体")
public class ExpProcedureVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 流程名称 */
    @ApiModelProperty("流程名称")
    private String name;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    /** 工序数量 */
    @ApiModelProperty("工序数量")
    private Integer processCount;

    /** 时长(分钟) */
    @ApiModelProperty("时长(分钟)")
    private Integer duration;

    //工序步骤列表
    private List<ExpProcessItemVO> processItemList;
    //工艺消耗耗材
    private List<ExpProcedureConsumableVO> processConsumableList;

}
