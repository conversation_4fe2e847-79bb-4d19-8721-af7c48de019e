package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 仓库管理 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("仓库管理回传实体")
public class DevWarehouseVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    private String code;

    private Integer regionId;

    /** 仓库名称 */
    @ApiModelProperty("仓库名称")
    private String name;

    /** 所在区域 */
    @ApiModelProperty("所在区域")
    private String region;

    /** 详细地址 */
    @ApiModelProperty("详细地址")
    private String address;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 经度 */
    @ApiModelProperty("经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    private String latitude;

    /** 仓库负责人 */
    @ApiModelProperty("仓库负责人")
    private String manager;

    /** 负责人联系电话 */
    @ApiModelProperty("负责人联系电话")
    private String managerContact;

    /** 状态(1.启用 0.禁用) */
    @ApiModelProperty("状态(1.启用 0.禁用)")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


    private Integer stock;


}
