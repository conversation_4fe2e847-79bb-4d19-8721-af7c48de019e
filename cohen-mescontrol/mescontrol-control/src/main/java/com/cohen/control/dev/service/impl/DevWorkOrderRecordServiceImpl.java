package com.cohen.control.dev.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.dev.converter.DevWorkOrderRecordConverter;
import com.cohen.control.dev.mapper.DevWorkOrderRecordMapper;
import com.cohen.control.dev.pojo.entity.DevWorkOrderRecord;
import com.cohen.control.dev.pojo.form.DevWorkOrderRecordForm;
import com.cohen.control.dev.pojo.po.DevWorkOrderRecordPO;
import com.cohen.control.dev.pojo.query.DevWorkOrderRecordPageQuery;
import com.cohen.control.dev.pojo.vo.DevWorkOrderRecordVO;
import com.cohen.control.dev.service.DevWorkOrderRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 工单处理记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
@RequiredArgsConstructor
public class DevWorkOrderRecordServiceImpl extends ServiceImpl<DevWorkOrderRecordMapper, DevWorkOrderRecord>implements DevWorkOrderRecordService {

    private final DevWorkOrderRecordConverter devWorkOrderRecordConverter;


    @Override
    public IPage<DevWorkOrderRecordVO> listDevWorkOrderRecordPages(DevWorkOrderRecordPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<DevWorkOrderRecordPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<DevWorkOrderRecordPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<DevWorkOrderRecordVO> result = devWorkOrderRecordConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public DevWorkOrderRecordVO getDevWorkOrderRecordData(Long id) {
        DevWorkOrderRecord sd = this.getById(id);
        DevWorkOrderRecordVO DevWorkOrderRecordVO = devWorkOrderRecordConverter.entity2Vo(sd);
        return DevWorkOrderRecordVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveDevWorkOrderRecord(DevWorkOrderRecordForm form) {
        DevWorkOrderRecord DevWorkOrderRecord = devWorkOrderRecordConverter.form2Entity(form);
        boolean result = this.save(DevWorkOrderRecord);
        return result;
    }

    @Override
    public boolean updateDevWorkOrderRecord(Long id, DevWorkOrderRecordForm form) {
        DevWorkOrderRecord ahDoctor = devWorkOrderRecordConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteDevWorkOrderRecord(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
