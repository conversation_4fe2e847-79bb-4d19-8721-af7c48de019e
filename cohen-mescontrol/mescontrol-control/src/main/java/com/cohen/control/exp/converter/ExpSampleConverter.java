package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.control.exp.pojo.entity.ExpSample;
import com.cohen.control.exp.pojo.form.ExpSampleForm;
import com.cohen.control.exp.pojo.po.ExpSamplePO;
import com.cohen.control.exp.pojo.vo.ExpSampleVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 脐带入库单明细（脐带管理）转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpSampleConverter {

    ExpSampleVO po2Vo(ExpSamplePO po);
    Page<ExpSampleVO> po2Vo(Page<ExpSamplePO> po);
    ExpSampleVO entity2Vo(ExpSample entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpSample form2Entity(ExpSampleForm form);

    List<ExpSample> form2Entity(List<ExpSampleForm> sampleItemList);

    List<ExpSampleVO> entity2Vo(List<ExpSample> list);
}
