
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCellDetectionRecord;
import com.cohen.control.exp.pojo.form.ExpCellDetectionRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellDetectionRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellDetectionRecordVO;


/**
 * 细胞记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCellDetectionRecordService extends IService<ExpCellDetectionRecord> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCellDetectionRecordVO> listExpCellDetectionRecordPages(ExpCellDetectionRecordPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCellDetectionRecordVO getExpCellDetectionRecordData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCellDetectionRecord(ExpCellDetectionRecordForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCellDetectionRecord(Long id, ExpCellDetectionRecordForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCellDetectionRecord(String idsStr);
}


