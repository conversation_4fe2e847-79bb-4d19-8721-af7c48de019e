
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCultivateReport;
import com.cohen.control.exp.pojo.form.ExpCultivateReportForm;
import com.cohen.control.exp.pojo.query.ExpCultivateReportPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateReportVO;


/**
 * 培养报告Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCultivateReportService extends IService<ExpCultivateReport> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCultivateReportVO> listExpCultivateReportPages(ExpCultivateReportPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCultivateReportVO getExpCultivateReportData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCultivateReport(ExpCultivateReportForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCultivateReport(Long id, ExpCultivateReportForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCultivateReport(String idsStr);
}


