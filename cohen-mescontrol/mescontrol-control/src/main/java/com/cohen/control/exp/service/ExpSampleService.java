
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpSample;
import com.cohen.control.exp.pojo.form.ExpSampleForm;
import com.cohen.control.exp.pojo.query.ExpSamplePageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleVO;
import com.cohen.control.exp.pojo.vo.IndexCountAllVO;
import com.cohen.control.exp.pojo.vo.QidaiShengChanStatisticsVO;
import com.cohen.control.exp.pojo.vo.XibaoShengChanStatisticsVO;

import java.util.List;


/**
 * 脐带管理 Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpSampleService extends IService<ExpSample> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpSampleVO> listExpSamplePages(ExpSamplePageQuery queryParams);


    IndexCountAllVO indexCount();

    XibaoShengChanStatisticsVO  xibaoShengChanStatistics();

    QidaiShengChanStatisticsVO qidaiShengChanStatistics();

    Integer getRuKuNum();
    Integer getChuKuNum();

    Integer getWanchengNum();

    /**
     * 获取详情数据

     *
     * @param id
     * @return
     */
    ExpSampleVO getExpSampleData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpSample(ExpSampleForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpSample(Long id, ExpSampleForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpSample(String idsStr);

    /**
     * 查询计划绑定的样品列表
     * @param planId
     * @return
     */
    List<ExpSampleVO> getPlanBindSample(Long planId);
}


