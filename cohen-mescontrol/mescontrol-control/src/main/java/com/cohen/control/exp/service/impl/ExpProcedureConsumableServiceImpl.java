package com.cohen.control.exp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.exp.mapper.ExpProcedureConsumableMapper;
import com.cohen.control.exp.pojo.entity.ExpProcedureConsumable;
import com.cohen.control.exp.service.ExpProcedureConsumableService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 培养计划绑定工序流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpProcedureConsumableServiceImpl extends ServiceImpl<ExpProcedureConsumableMapper, ExpProcedureConsumable>implements ExpProcedureConsumableService {
    
    
}
