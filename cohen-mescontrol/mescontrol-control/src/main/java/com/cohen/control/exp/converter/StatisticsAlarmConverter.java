package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.control.exp.pojo.entity.StatisticsAlarm;
import com.cohen.control.exp.pojo.po.StatisticsAlarmPO;
import com.cohen.control.exp.pojo.vo.StatisticsAlarmVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 脐带入库单明细（脐带管理）转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface StatisticsAlarmConverter {

    StatisticsAlarmVO po2Vo(StatisticsAlarmPO po);
    Page<StatisticsAlarmVO> po2Vo(Page<StatisticsAlarmPO> po);
    StatisticsAlarmVO entity2Vo(StatisticsAlarm entity);


}
