
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCultivateProcedure;
import com.cohen.control.exp.pojo.form.ExpCultivateProcedureForm;
import com.cohen.control.exp.pojo.query.ExpCultivateProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateProcedureVO;


/**
 * 培养计划绑定工序流程Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCultivateProcedureService extends IService<ExpCultivateProcedure> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCultivateProcedureVO> listExpCultivateProcedurePages(ExpCultivateProcedurePageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCultivateProcedureVO getExpCultivateProcedureData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCultivateProcedure(ExpCultivateProcedureForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCultivateProcedure(Long id, ExpCultivateProcedureForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCultivateProcedure(String idsStr);
}


