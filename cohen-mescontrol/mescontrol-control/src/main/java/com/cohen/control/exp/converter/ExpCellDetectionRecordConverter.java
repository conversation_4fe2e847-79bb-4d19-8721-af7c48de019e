package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.control.exp.pojo.entity.ExpCellDetectionRecord;
import com.cohen.control.exp.pojo.form.ExpCellDetectionRecordForm;
import com.cohen.control.exp.pojo.po.ExpCellDetectionRecordPO;
import com.cohen.control.exp.pojo.vo.ExpCellDetectionRecordVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * 细胞库存转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCellDetectionRecordConverter {

    ExpCellDetectionRecordVO po2Vo(ExpCellDetectionRecordPO po);
    Page<ExpCellDetectionRecordVO> po2Vo(Page<ExpCellDetectionRecordPO> po);
    ExpCellDetectionRecordVO entity2Vo(ExpCellDetectionRecord entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCellDetectionRecord form2Entity(ExpCellDetectionRecordForm form);
}
