
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCultivateTask;
import com.cohen.control.exp.pojo.form.ExpCultivateTaskForm;
import com.cohen.control.exp.pojo.query.ExpCultivateTaskPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateTaskInfoVO;
import com.cohen.control.exp.pojo.vo.ExpCultivateTaskVO;


/**
 * 培养任务Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCultivateTaskService extends IService<ExpCultivateTask> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCultivateTaskVO> listExpCultivateTaskPages(ExpCultivateTaskPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCultivateTaskVO getExpCultivateTaskData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCultivateTask(ExpCultivateTaskForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCultivateTask(Long id, ExpCultivateTaskForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCultivateTask(String idsStr);

    /**
     * 分配人员
     * @param form
     * @return
     */
    boolean assignUser(ExpCultivateTaskForm form);

    /**
     * 更换人员
     * @param form
     * @return
     */
    boolean changeAssign(ExpCultivateTaskForm form);

    /**
     * 批量分配人员
     * @param form
     * @return
     */
    boolean batchAssign(ExpCultivateTaskForm form);

    /**
     * 终止任务
     * @param form
     * @return
     */
    boolean terminateTask(ExpCultivateTaskForm form);

    /**
     * 培养任务表单数据
     * @param id
     * @return
     */
    ExpCultivateTaskInfoVO getExpCultivateTaskDetail(Long id);

    /**
     * 发送任务到队列
     */
    void sendTasksToQueue(String planNo);
}


