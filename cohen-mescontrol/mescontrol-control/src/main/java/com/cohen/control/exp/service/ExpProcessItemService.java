
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpProcessItem;
import com.cohen.control.exp.pojo.form.ExpProcessItemForm;
import com.cohen.control.exp.pojo.query.ExpProcessItemPageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcessItemVO;

import java.util.Map;


/**
 * 工序项Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpProcessItemService extends IService<ExpProcessItem> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpProcessItemVO> listExpProcessItemPages(ExpProcessItemPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpProcessItemVO getExpProcessItemData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpProcessItem(ExpProcessItemForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpProcessItem(Long id, ExpProcessItemForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpProcessItem(String idsStr);

    /**
     * 查询工序中设备参数
     * @param procedureId
     * @param deviceId
     * @return
     */
    Map<String, Object> getProcessDeviceParams(Long procedureId, Long deviceId,Integer stepNumber);

}


