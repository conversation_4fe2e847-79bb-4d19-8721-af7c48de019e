package com.cohen.control.dev.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.enums.DeleteEnum;
import com.cohen.common.web.exception.BizException;
import com.cohen.control.dev.converter.DevDeviceMaintenanceRulesConverter;
import com.cohen.control.dev.mapper.DevDeviceMaintenanceRulesMapper;
import com.cohen.control.dev.mapper.DevDeviceMapper;
import com.cohen.control.dev.pojo.entity.DevDevice;
import com.cohen.control.dev.pojo.entity.DevDeviceMaintenanceRules;
import com.cohen.control.dev.pojo.form.DevDeviceMaintenanceRulesForm;
import com.cohen.control.dev.pojo.po.DevDeviceMaintenanceRulesPO;
import com.cohen.control.dev.pojo.query.DevDeviceMaintenanceRulesPageQuery;
import com.cohen.control.dev.pojo.vo.DevDeviceMaintenanceRulesVO;
import com.cohen.control.dev.service.DevDeviceMaintenanceRulesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 保养规则关联设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
@RequiredArgsConstructor
public class DevDeviceMaintenanceRulesServiceImpl extends ServiceImpl<DevDeviceMaintenanceRulesMapper, DevDeviceMaintenanceRules>implements DevDeviceMaintenanceRulesService {

    private final DevDeviceMaintenanceRulesConverter devDeviceMaintenanceRulesConverter;

    private final DevDeviceMapper deviceMapper;



    @Override
    public IPage<DevDeviceMaintenanceRulesVO> listDevDeviceMaintenanceRulesPages(DevDeviceMaintenanceRulesPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<DevDeviceMaintenanceRulesPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<DevDeviceMaintenanceRulesPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<DevDeviceMaintenanceRulesVO> result = devDeviceMaintenanceRulesConverter.po2Vo(PoPage);

        return result;
    }

    @Override
    public IPage<DevDeviceMaintenanceRulesVO> notBindDeviceList(DevDeviceMaintenanceRulesPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<DevDeviceMaintenanceRulesPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<DevDeviceMaintenanceRulesPO> PoPage = this.baseMapper.notBindDeviceList(page, queryParams);

        // 实体转换
        Page<DevDeviceMaintenanceRulesVO> result = devDeviceMaintenanceRulesConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public DevDeviceMaintenanceRulesVO getDevDeviceMaintenanceRulesData(Long id) {
        DevDeviceMaintenanceRules sd = this.getById(id);
        DevDeviceMaintenanceRulesVO DevDeviceMaintenanceRulesVO = devDeviceMaintenanceRulesConverter.entity2Vo(sd);
        return DevDeviceMaintenanceRulesVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveDevDeviceMaintenanceRules(DevDeviceMaintenanceRulesForm form) {
        DevDeviceMaintenanceRules devDeviceMaintenanceRules = devDeviceMaintenanceRulesConverter.form2Entity(form);
        DevDevice device = this.deviceMapper.selectOne(new LambdaQueryWrapper<DevDevice>()
                .eq(DevDevice::getId,form.getDeviceId())
                .eq(DevDevice::getDelFlag, DeleteEnum.UNDELETED.getValue())
        );
        Assert.isTrue(device!=null,"找不到设备");

        DevDeviceMaintenanceRules maintenanceRules = this.getOne(new LambdaQueryWrapper<DevDeviceMaintenanceRules>()
                .eq(DevDeviceMaintenanceRules::getDeviceId,form.getDeviceId())
                .eq(DevDeviceMaintenanceRules::getRuleId,form.getRuleId())
        );
        Assert.isTrue(maintenanceRules!=null,"该设备已经绑定");

        boolean result = this.save(devDeviceMaintenanceRules);
        return result;
    }

    @Override
    public boolean updateDevDeviceMaintenanceRules(Long id, DevDeviceMaintenanceRulesForm form) {
        DevDeviceMaintenanceRules ahDoctor = devDeviceMaintenanceRulesConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteDevDeviceMaintenanceRules(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }

    /**
     * 新增告警规则关联设备
     * @param form
     * @return
     */
    @Override
    @Transactional
    public boolean addDeviceAndRules(List<DevDeviceMaintenanceRulesForm> form) {
        ArrayList<DevDeviceMaintenanceRules> list = new ArrayList<>();
        for (DevDeviceMaintenanceRulesForm data : form) {
            DevDevice device = this.deviceMapper.selectOne(new LambdaQueryWrapper<DevDevice>()
                    .eq(DevDevice::getId,data.getDeviceId())
                    .eq(DevDevice::getDelFlag, DeleteEnum.UNDELETED.getValue())
            );
            if(device == null){
                throw new BizException("找不到设备");
            }

            DevDeviceMaintenanceRules maintenanceRules = this.getOne(new LambdaQueryWrapper<DevDeviceMaintenanceRules>()
                    .eq(DevDeviceMaintenanceRules::getDeviceId,data.getDeviceId())
                    .eq(DevDeviceMaintenanceRules::getRuleId,data.getRuleId())
            );
            if (maintenanceRules !=null){
                throw new BizException("该设备已经绑定");
            }

            DevDeviceMaintenanceRules DevDeviceMaintenanceRules = devDeviceMaintenanceRulesConverter.form2Entity(data);
            list.add(DevDeviceMaintenanceRules);
        }
        boolean result = this.saveBatch(list);
        return result;
    }
}
