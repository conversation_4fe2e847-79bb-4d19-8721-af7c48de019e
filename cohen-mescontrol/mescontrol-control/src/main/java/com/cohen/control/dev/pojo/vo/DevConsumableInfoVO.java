package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 耗材基础信息 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材基础信息回传实体")
public class DevConsumableInfoVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 耗材名称 */
    @ApiModelProperty("耗材名称")
    private String name;

    /** 所属类型id */
    @ApiModelProperty("所属类型id")
    private Long consumableTypeId;

    /** 所属类型 */
    @ApiModelProperty("所属类型")
    private String consumableTypeName;

    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String model;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 预警库存 */
    @ApiModelProperty("预警库存")
    private Integer warningInventory;

    /** 所属品牌 */
    @ApiModelProperty("所属品牌")
    private String brand;

    /** 生产厂家 */
    @ApiModelProperty("生产厂家")
    private String manufacturer;

    /** 耗材图片 */
    @ApiModelProperty("耗材图片")
    private String picture;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


    private List<DevConsumableInventoryVO> inventoryVOList;

}
