package com.cohen.control.dev.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 维修师傅详情 回传实体
 * 
 * <AUTHOR>
 * @date 2024-01-12
 */
@Data
@ApiModel("维修师傅详情回传实体")
public class DevMaintenanceVO {

    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;

    /** 昵称 */
    @ApiModelProperty("昵称")
    private String nickname;

    /** 姓名 */
    @ApiModelProperty("姓名")
    private String name;

    /** 手机号 */
    @ApiModelProperty("手机号")
    private String phone;

    /** 入职时间 */
    @ApiModelProperty("入职时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate inboundTime;

    /** 身份证号 */
    @ApiModelProperty("身份证号")
    private String idCard;

    /** 身份证人像图 */
    @ApiModelProperty("身份证人像图")
    private String idCardJust;

    /** 身份证国徽图 */
    @ApiModelProperty("身份证国徽图")
    private String idCardBack;

    /** 所在区域id */
    @ApiModelProperty("所在区域id")
    private Long regionId;

    /** 所在区域 */
    @ApiModelProperty("所在区域")
    private String region;

    /** 详细地址 */
    @ApiModelProperty("详细地址")
    private String address;

    /** 管辖区域(json) */
    @ApiModelProperty("管辖区域(json)")
    private String governmentRegion;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 状态(1启用0禁用) */
    @ApiModelProperty("状态(1启用0禁用)")
    private Integer status;

    /** 创建人账号(扩展字段) */
    @ApiModelProperty("创建人账号(扩展字段)")
    private String createBy;

    /** 创建人名称(扩展字段) */
    @ApiModelProperty("创建人名称(扩展字段)")
    private String createName;

    /** 创建时间(扩展字段) */
    @ApiModelProperty("创建时间(扩展字段)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 修改人账号(扩展字段) */
    @ApiModelProperty("修改人账号(扩展字段)")
    private String updateBy;

    /** 修改时间(扩展字段) */
    @ApiModelProperty("修改时间(扩展字段)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 是否删除(扩展字段:1删除0未删除) */
    @ApiModelProperty("是否删除(扩展字段:1删除0未删除)")
    private Integer delFlag;

    /** 预留字段(扩展字段) */
    @ApiModelProperty("预留字段(扩展字段)")
    private String extra;


}
