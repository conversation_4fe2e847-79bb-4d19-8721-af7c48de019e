package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 耗材类型 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材类型回传实体")
public class DevConsumableTypeVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 类型名称 */
    @ApiModelProperty("类型名称")
    private String name;

    /** 所属上级 */
    @ApiModelProperty("所属上级")
    private Long parent;

    /** 祖级id */
    @ApiModelProperty("祖级id")
    private String acester;

    /** 显示顺序 */
    @ApiModelProperty("显示顺序")
    private Integer sort;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 状态(1.启用 0.禁用) */
    @ApiModelProperty("状态(1.启用 0.禁用)")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    //父节点名称
    private String parentName;

    List<DevConsumableTypeVO> children;

}
