package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.enums.SampleStatusEnum;
import com.cohen.common.result.Result;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.utils.RandomLabelUtils;
import com.cohen.common.web.exception.BizException;
import com.cohen.control.exp.converter.ExpSampleItemConverter;
import com.cohen.control.exp.pojo.entity.ExpSample;
import com.cohen.control.exp.pojo.entity.ExpSampleItem;
import com.cohen.control.exp.pojo.form.ExpSampleItemForm;
import com.cohen.control.exp.pojo.vo.ExpSampleItemVO;
import com.cohen.control.exp.service.ExpSampleItemService;
import com.cohen.control.exp.service.ExpSampleService;
import com.cohen.system.api.UserFeignClient;
import com.cohen.system.form.AdminForm;
import com.sun.org.apache.bcel.internal.generic.NEW;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpSampleInboundMapper;
import com.cohen.control.exp.pojo.entity.ExpSampleInbound;
import com.cohen.control.exp.pojo.form.ExpSampleInboundForm;
import com.cohen.control.exp.pojo.po.ExpSampleInboundPO;
import com.cohen.control.exp.pojo.query.ExpSampleInboundPageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleInboundVO;
import com.cohen.control.exp.service.ExpSampleInboundService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 脐带入库单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpSampleInboundServiceImpl extends ServiceImpl<ExpSampleInboundMapper, ExpSampleInbound>implements ExpSampleInboundService {

    private final com.cohen.control.exp.converter.ExpSampleInboundConverter ExpSampleInboundConverter;
    private final UserFeignClient userFeignClient;
    private final ExpSampleItemConverter expSampleItemConverter;
    private final ExpSampleItemService expSampleItemService;
    private final ExpSampleService expSampleService;

    @Override
    public IPage<ExpSampleInboundVO> listExpSampleInboundPages(ExpSampleInboundPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpSampleInboundPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpSampleInboundPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpSampleInboundVO> result = ExpSampleInboundConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpSampleInboundVO getExpSampleInboundData(Long id) {
        ExpSampleInbound sd = this.getById(id);
        ExpSampleInboundVO ExpSampleInboundVO = ExpSampleInboundConverter.entity2Vo(sd);
        List<ExpSampleItem> list = expSampleItemService.list(new LambdaQueryWrapper<ExpSampleItem>()
                .eq(ExpSampleItem::getInboundNo, sd.getInboundNo()));
        List<Map<String, Object>> itemMap = new LinkedList<>();
        list.stream().forEach(item ->{
            Map<String, Object> map = new HashMap<>();
            map.put("id",item.getId());
            map.put("sampleNo",item.getSampleNo());
            map.put("donorName",item.getDonorName());
            map.put("donorAge",item.getDonorAge());
            map.put("healthStatus",item.getHealthStatus());
            itemMap.add(map);
        });
        ExpSampleInboundVO.setSampleItemList(itemMap);
        return ExpSampleInboundVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    @Transactional
    public boolean saveExpSampleInbound(ExpSampleInboundForm form) {
        int count = this.count(new LambdaQueryWrapper<ExpSampleInbound>()
                .eq(ExpSampleInbound::getInboundNo, form.getInboundNo()));
        Assert.isTrue(count<=0,"入库单号已存在");
        ExpSampleInbound expSampleInbound = ExpSampleInboundConverter.form2Entity(form);
        //查询用户信息
//        Result<AdminForm> adminFormResult = userFeignClient.getAdminDetail(SecurityUtils.getUserId());
//        Assert.isTrue(Result.isSuccess(adminFormResult),"查询用户信息失败");
//        AdminForm adminForm = adminFormResult.getData();
        expSampleInbound.setOperator(SecurityUtils.getNickname());
        expSampleInbound.setOperatorTime(new Date());
        expSampleInbound.setOperatorContactPhone(SecurityUtils.getMobile());
        expSampleInbound.setNum(form.getSampleItemList().size());
        boolean result = this.save(expSampleInbound);

        if (CollectionUtils.isNotEmpty(form.getSampleItemList())){
            List<ExpSampleItem> sampleItemList = expSampleItemConverter.form2Entity(form.getSampleItemList());

            ArrayList<ExpSample> expSampleList = new ArrayList<>();

            for (ExpSampleItem expSampleItem : sampleItemList) {
                expSampleItem.setInboundNo(expSampleInbound.getInboundNo());
                expSampleItem.setSampleNo(RandomLabelUtils.generateUniqueCode());
                //样品信息
                ExpSample expSample = new ExpSample();
                expSample.setSampleNo(expSampleItem.getSampleNo());
                expSample.setInboundNo(expSampleInbound.getInboundNo());
                expSample.setBatchNumber(expSampleInbound.getBatchNumber());
                expSample.setDonorName(expSampleItem.getDonorName());
                expSample.setDonorAge(expSampleItem.getDonorAge());
                expSample.setHealthStatus(expSampleItem.getHealthStatus());
                expSample.setRemark(expSampleInbound.getRemark());
                expSample.setResource(expSampleInbound.getResource());
                expSample.setResourceContact(expSampleInbound.getResourceContact());
                expSample.setResourceContactPhone(expSampleInbound.getResourceContactPhone());
                expSample.setReceivingTime(expSampleInbound.getReceivingTime());
                expSample.setOperator(expSampleInbound.getOperator());
                expSample.setOperatorTime(expSampleInbound.getOperatorTime());
                expSampleList.add(expSample);
            }
            //保存样品入库明细
            expSampleItemService.saveBatch(sampleItemList);
            //保存样品
            expSampleService.saveBatch(expSampleList);
        }

        return result;
    }

    @Override
    @Transactional
    public boolean updateExpSampleInbound(Long id, ExpSampleInboundForm form) {
        ExpSampleInbound expSampleInbound = ExpSampleInboundConverter.form2Entity(form);
        expSampleInbound.setNum(form.getSampleItemList().size());
        boolean result = this.updateById(expSampleInbound);

        //处理入库单明细
        if (CollectionUtils.isNotEmpty(form.getSampleItemIds())){
            for (Long sampleItemId : form.getSampleItemIds()) {
                //查询当前明细并且删除
                ExpSampleItem sampleItem = expSampleItemService.getById(sampleItemId);
                expSampleItemService.removeById(sampleItemId);
                //查询明细对应的样品
                ExpSample expSample = expSampleService.getOne(new LambdaQueryWrapper<ExpSample>()
                        .eq(ExpSample::getSampleNo, sampleItem.getSampleNo())
                        .eq(ExpSample::getInboundNo,form.getInboundNo()));
                if (expSample.getProductionStatus()!= SampleStatusEnum.PENDING_PRODUCTION.getValue()){
                    throw new BizException("编号:"+sampleItem.getSampleNo()+"不允许删除");
                }
                expSampleService.removeById(expSample.getId());
            }
        }


        List<ExpSampleItem> sampleItemList = expSampleItemConverter.form2Entity(form.getSampleItemList());
        for (ExpSampleItem expSampleItem : sampleItemList) {
            int count = expSampleItemService.count(new LambdaQueryWrapper<ExpSampleItem>()
                    .eq(ExpSampleItem::getSampleNo, expSampleItem.getSampleNo())
                    .ne(ExpSampleItem::getId, expSampleItem.getId()));
            Assert.isTrue(count<=0,expSampleItem.getSampleNo()+"编号已存在");
            if (expSampleItem.getId()==null){
                //新增明细
                expSampleItemService.save(expSampleItem);

                ExpSample expSample = new ExpSample();
                expSample.setSampleNo(expSampleItem.getSampleNo());
                expSample.setInboundNo(expSampleInbound.getInboundNo());
                expSample.setBatchNumber(expSampleInbound.getBatchNumber());
                expSample.setResource(expSampleInbound.getResource());
                expSample.setResourceContact(expSampleInbound.getResourceContact());
                expSample.setResourceContactPhone(expSampleInbound.getResourceContactPhone());
                expSample.setReceivingTime(expSampleInbound.getReceivingTime());
                expSample.setOperator(expSampleInbound.getOperator());
                expSample.setOperatorTime(expSampleInbound.getOperatorTime());
                expSampleService.save(expSample);
            }else {
                expSampleItemService.updateById(expSampleItem);

                expSampleService.update(new LambdaUpdateWrapper<ExpSample>()
                        .eq(ExpSample::getSampleNo,expSampleItem.getSampleNo())
                        .eq(ExpSample::getInboundNo,expSampleItem.getInboundNo())
                        .set(ExpSample::getResource,expSampleInbound.getResource())
                        .set(ExpSample::getResourceContact,expSampleInbound.getResourceContact())
                        .set(ExpSample::getResourceContactPhone,expSampleInbound.getResourceContactPhone())
                        .set(ExpSample::getReceivingTime,expSampleInbound.getReceivingTime())
                        .set(ExpSample::getOperator,expSampleInbound.getOperator())
                        .set(ExpSample::getOperatorTime,expSampleInbound.getOperatorTime()));
            }
        }
        return result;
    }

    @Override
    public boolean deleteExpSampleInbound(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
