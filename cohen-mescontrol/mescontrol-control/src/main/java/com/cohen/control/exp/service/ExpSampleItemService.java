
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpSampleItem;
import com.cohen.control.exp.pojo.form.ExpSampleItemForm;
import com.cohen.control.exp.pojo.query.ExpSampleItemPageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleItemVO;


/**
 * 脐带入库单明细（脐带管理）Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpSampleItemService extends IService<ExpSampleItem> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpSampleItemVO> listExpSampleItemPages(ExpSampleItemPageQuery queryParams);

    /**
     * 获取详情数据

     *
     * @param id
     * @return
     */
    ExpSampleItemVO getExpSampleItemData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpSampleItem(ExpSampleItemForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpSampleItem(Long id, ExpSampleItemForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpSampleItem(String idsStr);
}


