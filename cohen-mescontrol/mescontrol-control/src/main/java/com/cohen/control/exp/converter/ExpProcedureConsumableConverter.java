package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cohen.control.exp.pojo.entity.ExpProcedureConsumable;
import com.cohen.control.exp.pojo.form.ExpProcedureConsumableForm;
import com.cohen.control.exp.pojo.po.ExpProcedureConsumablePO;
import com.cohen.control.exp.pojo.vo.ExpProcedureConsumableVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 细胞记录转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpProcedureConsumableConverter {

    ExpProcedureConsumableVO po2Vo(ExpProcedureConsumablePO po);
    Page<ExpProcedureConsumableVO> po2Vo(Page<ExpProcedureConsumablePO> po);
    ExpProcedureConsumableVO entity2Vo(ExpProcedureConsumable entity);

    List<ExpProcedureConsumableVO> entity3Vo(List<ExpProcedureConsumable> entity);

    List<ExpProcedureConsumable> form3Entity(List<ExpProcedureConsumableForm> form);


    @InheritInverseConfiguration(name = "entity2Form")
    ExpProcedureConsumable form2Entity(ExpProcedureConsumableForm form);
}
