package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpProcessItemMapper;
import com.cohen.control.exp.pojo.entity.ExpProcessItem;
import com.cohen.control.exp.pojo.form.ExpProcessItemForm;
import com.cohen.control.exp.pojo.po.ExpProcessItemPO;
import com.cohen.control.exp.pojo.query.ExpProcessItemPageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcessItemVO;
import com.cohen.control.exp.service.ExpProcessItemService;

/**
 * 工序项Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpProcessItemServiceImpl extends ServiceImpl<ExpProcessItemMapper, ExpProcessItem>implements ExpProcessItemService {

    private final com.cohen.control.exp.converter.ExpProcessItemConverter ExpProcessItemConverter;


    @Override
    public IPage<ExpProcessItemVO> listExpProcessItemPages(ExpProcessItemPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpProcessItemPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpProcessItemPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpProcessItemVO> result = ExpProcessItemConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpProcessItemVO getExpProcessItemData(Long id) {
        ExpProcessItem sd = this.getById(id);
        ExpProcessItemVO ExpProcessItemVO = ExpProcessItemConverter.entity2Vo(sd);
        return ExpProcessItemVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpProcessItem(ExpProcessItemForm form) {
        ExpProcessItem ExpProcessItem = ExpProcessItemConverter.form2Entity(form);
        boolean result = this.save(ExpProcessItem);
        return result;
    }

    @Override
    public boolean updateExpProcessItem(Long id, ExpProcessItemForm form) {
        ExpProcessItem ahDoctor = ExpProcessItemConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpProcessItem(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }

    @Override
    public Map<String, Object> getProcessDeviceParams(Long procedureId, Long deviceId,Integer stepNumber) {
        return  this.baseMapper.getProcessDeivceParams(procedureId,deviceId,stepNumber);
    }
}
