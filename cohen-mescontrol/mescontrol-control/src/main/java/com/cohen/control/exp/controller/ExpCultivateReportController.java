package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateReport;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCultivateReportForm;
import com.cohen.control.exp.pojo.query.ExpCultivateReportPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateReportVO;
import com.cohen.control.exp.service.ExpCultivateReportService;


/**
 * 培养报告Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "培养报告接口")
@RestController
@RequestMapping("/api/v1/exp/report")
@RequiredArgsConstructor
public class ExpCultivateReportController
{
    private final ExpCultivateReportService expCultivateReportService;

    @ApiOperation(value = "培养报告分页列表")
    @GetMapping("/pages")
        public PageResult listExpCultivateReportPages(ExpCultivateReportPageQuery queryParams){
            IPage<ExpCultivateReportVO> result = expCultivateReportService.listExpCultivateReportPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "培养报告表单数据")
    @PreAuthorize("@pms.hasPermission('exp:report:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCultivateReportDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCultivateReportVO vo = expCultivateReportService.getExpCultivateReportData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增培养报告")
    @Log(title = "新增培养报告",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:report:add')")
    @PostMapping
    public Result saveExpCultivateReport(
            @RequestBody @Valid ExpCultivateReportForm form
    ) {
        boolean result = expCultivateReportService.saveExpCultivateReport(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改培养报告")
    @Log(title = "修改培养报告",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:report:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCultivateReport(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCultivateReportForm form
    ) {
        boolean result = expCultivateReportService.updateExpCultivateReport(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除培养报告")
    @Log(title = "删除培养报告",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:report:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCultivateReport(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCultivateReportService.deleteExpCultivateReport(ids);
        return Result.judge(result);
    }

    @ApiOperation(value = "查询报告异常数量")
    @GetMapping("/getExceptionCount")
    public Result getExceptionCount(){
        int count = expCultivateReportService.count(new LambdaQueryWrapper<ExpCultivateReport>()
                .eq(ExpCultivateReport::getDetectionResult, 1));
        return Result.success(count);
    }
}
