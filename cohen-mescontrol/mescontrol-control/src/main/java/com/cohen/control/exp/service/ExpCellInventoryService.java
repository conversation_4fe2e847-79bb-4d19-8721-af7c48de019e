
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCellInventory;
import com.cohen.control.exp.pojo.form.ExpCellInventoryForm;
import com.cohen.control.exp.pojo.query.ExpCellInventoryPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellInventoryVO;
import org.apache.ibatis.annotations.Select;


/**
 * 细胞库存Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCellInventoryService extends IService<ExpCellInventory> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCellInventoryVO> listExpCellInventoryPages(ExpCellInventoryPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCellInventoryVO getExpCellInventoryData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCellInventory(ExpCellInventoryForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCellInventory(Long id, ExpCellInventoryForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCellInventory(String idsStr);

    //当前库存
    Integer getRuKuNum();

    Integer getChuKuNum();

    Integer getDaiRuKuNum();

}


