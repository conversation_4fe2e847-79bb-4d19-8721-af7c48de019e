package com.cohen.control.exp.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 脐带管理 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("脐带入库单明细（脐带管理）回传实体")
public class ExpSampleVO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 入库单号 */
    @ApiModelProperty("入库单号")
    private String inboundNo;

    /** 批次号 */
    @ApiModelProperty("批次号")
    private String batchNumber;

    /** 入库数量 */
    @ApiModelProperty("入库数量")
    private String num;

    /** 脐带编号 */
    @ApiModelProperty("脐带编号")
    private String sampleNo;

    /** 来源 */
    @ApiModelProperty("来源")
    private String resource;

    /** 来源联系人 */
    @ApiModelProperty("来源联系人")
    private String resourceContact;

    /** 来源联系方式 */
    @ApiModelProperty("来源联系方式")
    private String resourceContactPhone;

    /** 所属计划id */
    @ApiModelProperty("所属计划id")
    private Long planId;

    /** 所属计划 */
    @ApiModelProperty("所属计划")
    private String planName;

    /** 当前工序id */
    @ApiModelProperty("当前工序id")
    private Long workId;

    /** 所属任务id */
    @ApiModelProperty("所属任务id")
    private Long taskId;

    /** 所属任务 */
    @ApiModelProperty("所属任务")
    private String taskName;

    /** 接收时间 */
    @ApiModelProperty("接收时间")
    private Date receivingTime;

    /** 检测状态 */
    @ApiModelProperty("检测状态")
    private Integer detectionStatus;

    /** 检测结果(0.待检测 1.已合格 2.未合格) */
    @ApiModelProperty("检测结果(0.待检测 1.已合格 2.未合格)")
    private String detectionResult;

    /** 检测时间 */
    @ApiModelProperty("检测时间")
    private Date detectionTime;

    /** 生产状态(0.待生产 1.待开始 2.进行中 3.已停止 4.已完成) */
    @ApiModelProperty("生产状态(0.待生产 1.待开始 2.进行中 3.已停止 4.已完成)")
    private Integer productionStatus;

    /** 生产开始时间 */
    @ApiModelProperty("生产开始时间")
    private Date productionStartTime;

    /** 生产结束时间 */
    @ApiModelProperty("生产结束时间")
    private Date productionEndTime;

    /** 生产时长 */
    @ApiModelProperty("生产时长")
    private Integer productionDuration;

    /** 捐献者名称 */
    @ApiModelProperty("捐献者名称")
    private String donorName;

    /** 捐献者年龄 */
    @ApiModelProperty("捐献者年龄")
    private Integer donorAge;

    /** 健康状态 */
    @ApiModelProperty("健康状态")
    private String healthStatus;

    /** 捐赠时间 */
    @ApiModelProperty("捐赠时间")
    private Date donationTime;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 操作员 */
    @ApiModelProperty("操作员")
    private String operator;

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    private Date operatorTime;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    /** 计划编号 */
    private String planNo;

    //计划备注
    private String planRemark;


}
