package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.exp.converter.ExpCellInventoryConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCellInventoryMapper;
import com.cohen.control.exp.pojo.entity.ExpCellInventory;
import com.cohen.control.exp.pojo.form.ExpCellInventoryForm;
import com.cohen.control.exp.pojo.po.ExpCellInventoryPO;
import com.cohen.control.exp.pojo.query.ExpCellInventoryPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellInventoryVO;
import com.cohen.control.exp.service.ExpCellInventoryService;

/**
 * 细胞库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCellInventoryServiceImpl extends ServiceImpl<ExpCellInventoryMapper, ExpCellInventory>implements ExpCellInventoryService {

    private final ExpCellInventoryConverter expCellInventoryConverter;


    @Override
    public IPage<ExpCellInventoryVO> listExpCellInventoryPages(ExpCellInventoryPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCellInventoryPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCellInventoryPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCellInventoryVO> result = expCellInventoryConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCellInventoryVO getExpCellInventoryData(Long id) {
        ExpCellInventory sd = this.getById(id);
        ExpCellInventoryVO ExpCellInventoryVO = expCellInventoryConverter.entity2Vo(sd);
        return ExpCellInventoryVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCellInventory(ExpCellInventoryForm form) {
        ExpCellInventory ExpCellInventory = expCellInventoryConverter.form2Entity(form);
        boolean result = this.save(ExpCellInventory);
        return result;
    }

    @Override
    public boolean updateExpCellInventory(Long id, ExpCellInventoryForm form) {
        ExpCellInventory ahDoctor = expCellInventoryConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCellInventory(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }

    @Override
    public Integer getRuKuNum() {
        return baseMapper.getRuKuNum();
    }

    @Override
    public Integer getChuKuNum() {
        return baseMapper.getChuKuNum();
    }

    @Override
    public Integer getDaiRuKuNum() {
        return baseMapper.getDaiRuKuNum();
    }
}
