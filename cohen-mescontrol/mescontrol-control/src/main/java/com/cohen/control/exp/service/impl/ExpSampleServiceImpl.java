package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.exp.converter.ExpSampleConverter;
import com.cohen.control.exp.mapper.ExpCultivatePlanMapper;
import com.cohen.control.exp.mapper.ExpSampleInboundMapper;
import com.cohen.control.exp.mapper.ExpSampleMapper;
import com.cohen.control.exp.pojo.entity.ExpCultivatePlan;
import com.cohen.control.exp.pojo.entity.ExpPlanSample;
import com.cohen.control.exp.pojo.entity.ExpSampleInbound;
import com.cohen.control.exp.pojo.entity.ExpSample;
import com.cohen.control.exp.pojo.form.ExpSampleForm;
import com.cohen.control.exp.pojo.po.ExpSamplePO;
import com.cohen.control.exp.pojo.query.ExpSamplePageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleVO;
import com.cohen.control.exp.pojo.vo.IndexCountAllVO;
import com.cohen.control.exp.pojo.vo.QidaiShengChanStatisticsVO;
import com.cohen.control.exp.pojo.vo.XibaoShengChanStatisticsVO;
import com.cohen.control.exp.service.ExpPlanSampleService;
import com.cohen.control.exp.service.ExpSampleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 脐带管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpSampleServiceImpl extends ServiceImpl<ExpSampleMapper, ExpSample>implements ExpSampleService {

    private final ExpSampleConverter expSampleConverter;

    private final ExpSampleInboundMapper expSampleInboundMapper;
    private final ExpCultivatePlanMapper expCultivatePlanMapper;
    private final ExpPlanSampleService planSampleService;


    @Override
    public IPage<ExpSampleVO> listExpSamplePages(ExpSamplePageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpSamplePO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpSamplePO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpSampleVO> result = expSampleConverter.po2Vo(PoPage);

        return result;
    }

    @Override
    public IndexCountAllVO indexCount() {
        return baseMapper.indexCount();
    }

    @Override
    public XibaoShengChanStatisticsVO xibaoShengChanStatistics() {
        return baseMapper.xibaoShengChanStatistics();
    }

    @Override
    public QidaiShengChanStatisticsVO qidaiShengChanStatistics() {
        return baseMapper.qidaiShengChanStatistics();
    }

    @Override
    public Integer getRuKuNum() {
        return baseMapper.getRuKuNum();
    }

    @Override
    public Integer getChuKuNum() {
        return baseMapper.getChuKuNum();
    }

    @Override
    public Integer getWanchengNum() {
        return baseMapper.getWanchengNum();
    }


    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpSampleVO getExpSampleData(Long id) {
        ExpSample sd = this.getById(id);
        ExpSampleVO ExpSampleVO = expSampleConverter.entity2Vo(sd);
        //查询该样品绑定的计划信息
        if (sd.getPlanId()!=null){
            ExpCultivatePlan expCultivatePlan = expCultivatePlanMapper.selectById(sd.getPlanId());
            if (expCultivatePlan!=null){
                ExpSampleVO.setPlanNo(expCultivatePlan.getNo());
                ExpSampleVO.setPlanRemark(expCultivatePlan.getRemark());
            }
        }
        return ExpSampleVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpSample(ExpSampleForm form) {
        ExpSample ExpSample = expSampleConverter.form2Entity(form);
        boolean result = this.save(ExpSample);
        return result;
    }

    @Override
    public boolean updateExpSample(Long id, ExpSampleForm form) {
        ExpSample ahDoctor = expSampleConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpSample(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }


    @Override
    public List<ExpSampleVO> getPlanBindSample(Long planId) {
//        List<ExpPlanSample> planSampleList = planSampleService.list(new LambdaQueryWrapper<ExpPlanSample>()
//                .eq(ExpPlanSample::getPlanId, planId)
//                .select(ExpPlanSample::getSampleId));
//        List<Long> sampleId = planSampleList.stream().map(ExpPlanSample::getSampleId).collect(Collectors.toList());
//
//        List<ExpSample> list = this.list(new LambdaQueryWrapper<ExpSample>()
//                .in(ExpSample::getId, sampleId));
        List<ExpSample> list = this.list(new LambdaQueryWrapper<ExpSample>()
                .in(ExpSample::getPlanId, planId));
        List<ExpSampleVO> result = expSampleConverter.entity2Vo(list);
        return result;
    }
}
