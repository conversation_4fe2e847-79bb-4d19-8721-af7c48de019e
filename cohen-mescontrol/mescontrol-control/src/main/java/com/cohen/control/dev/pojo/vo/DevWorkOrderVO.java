package com.cohen.control.dev.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工单 回传实体
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("工单回传实体")
public class DevWorkOrderVO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 标题 */
    @ApiModelProperty("标题")
    private String title;

    /** 类型(1维修 2保养 3告警 4报废) */
    @ApiModelProperty("类型(1维修 2保养 3告警 4报废)")
    private Integer type;

    /** 工艺id */
    @ApiModelProperty("工艺id")
    private Long processId;

    /** 工艺名称 */
    @ApiModelProperty("工艺名称")
    private String processName;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /** 优先级(1一般 2优先 3紧急) */
    @ApiModelProperty("优先级(1一般 2优先 3紧急)")
    private Integer priority;

    /** 开始时间 */
    @ApiModelProperty("开始时间")
    private Date startTime;

    /** 要求完成时间 */
    @ApiModelProperty("要求完成时间")
    private Date deadline;

    /** 描述 */
    @ApiModelProperty("描述")
    private String description;

    /** 现场照片 */
    @ApiModelProperty("现场照片")
    private String sitePhotos;

    /** 状态(0待审批 1待处理 2已处理 3已取消) */
    @ApiModelProperty("状态(0待审批 1待处理 2已驳回 3已处理 4已取消)")
    private Integer status;

    /** 审批时间 */
    @ApiModelProperty("审批时间")
    private Date approvalTime;

    /** 驳回原因 */
    @ApiModelProperty("驳回原因")
    private String reason;

    /** 完成时间 */
    @ApiModelProperty("完成时间")
    private Date completionTime;

    /** 处理内容 */
    @ApiModelProperty("处理内容")
    private String processingContent;

    /** 处理照片 */
    @ApiModelProperty("处理照片")
    private String processingPhotos;

    /** 处理人id */
    @ApiModelProperty("处理人id")
    private Long processorId;

    /** 处理人(工单归属) */
    @ApiModelProperty("处理人(工单归属)")
    private String processor;

    /** 处理时间 */
    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processorTime;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    /** 流程实例id */
    @ApiModelProperty("流程实例id")
    private String procInsId;

    /** 任务id */
    @ApiModelProperty("任务id")
    private String taskId;

    private String processInstanceId;

    //站所名称
    private String stationName;

    private DevDeviceVO equipDevice;

    private List<DevWorkOrderRecordVO> recordList;


}
