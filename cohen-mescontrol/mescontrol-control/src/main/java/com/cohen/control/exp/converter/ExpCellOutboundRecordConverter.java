package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCellOutboundRecord;
import com.cohen.control.exp.pojo.form.ExpCellOutboundRecordForm;
import com.cohen.control.exp.pojo.po.ExpCellOutboundRecordPO;
import com.cohen.control.exp.pojo.vo.ExpCellOutboundRecordVO;

/**
 * 细胞出库记录转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCellOutboundRecordConverter {

    ExpCellOutboundRecordVO po2Vo(ExpCellOutboundRecordPO po);
    Page<ExpCellOutboundRecordVO> po2Vo(Page<ExpCellOutboundRecordPO> po);
    ExpCellOutboundRecordVO entity2Vo(ExpCellOutboundRecord entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCellOutboundRecord form2Entity(ExpCellOutboundRecordForm form);
}
