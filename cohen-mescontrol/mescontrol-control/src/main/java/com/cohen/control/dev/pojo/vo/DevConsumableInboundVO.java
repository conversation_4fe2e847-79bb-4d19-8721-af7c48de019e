package com.cohen.control.dev.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 耗材入库单 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材入库单回传实体")
public class DevConsumableInboundVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 入库单号 */
    @ApiModelProperty("入库单号")
    private String no;

    /** 批次号 */
    @ApiModelProperty("批次号")
    private String batchNumber;

    /** 入库数量 */
    @ApiModelProperty("入库数量")
    private Integer intoNum;

    /** 耗材产品id */
    @ApiModelProperty("耗材产品id")
    private Long consumableId;

    /** 耗材产品名称 */
    @ApiModelProperty("耗材产品名称")
    private String consumableName;

    /** 耗材品牌 */
    @ApiModelProperty("耗材品牌")
    private String consumableBrand;

    /** 耗材单价 */
    @ApiModelProperty("耗材单价")
    private Long price;

    /** 生产厂家 */
    @ApiModelProperty("生产厂家")
    private String manufacturer;

    /** 生产日期 */
    @ApiModelProperty("生产日期")
    private Date manufactureDate;

    /** 入库仓库id */
    @ApiModelProperty("入库仓库id")
    private Long warehouseId;

    /** 入库仓库 */
    @ApiModelProperty("入库仓库")
    private String warehouse;

    /** 入库时间 */
    @ApiModelProperty("入库时间")
    private Date warehouseDate;

    /** 联系人 */
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String contactPhone;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 耗材图片 */
    @ApiModelProperty("耗材图片")
    private String picture;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Long delFlag;

    //耗材类数
    private Integer typeNum;


    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    private List<DevConsumableInboundItemVO> inboundItemVOS;

    private List<DevConsumableVO> infoVOS;


}
