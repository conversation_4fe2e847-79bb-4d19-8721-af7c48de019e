package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 耗材库存统计 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("库存管理分组列表")
public class DevConsumableInventoryS1VO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 耗材id */
    @ApiModelProperty("耗材id")
    private Long consumableId;

    /** 耗材名称 */
    @ApiModelProperty("耗材名称")
    private String consumableName;

    private String consumableTypeName;

    /** 耗材数量 */
    @ApiModelProperty("耗材数量")
    private Integer num;

    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String model;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;


    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;

    /** 1 库存充足，2库存预警 */
    private Integer warnType;


}
