package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCellInventory;
import com.cohen.control.exp.pojo.form.ExpCellInventoryForm;
import com.cohen.control.exp.pojo.po.ExpCellInventoryPO;
import com.cohen.control.exp.pojo.vo.ExpCellInventoryVO;

/**
 * 细胞库存转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCellInventoryConverter {

    ExpCellInventoryVO po2Vo(ExpCellInventoryPO po);
    Page<ExpCellInventoryVO> po2Vo(Page<ExpCellInventoryPO> po);
    ExpCellInventoryVO entity2Vo(ExpCellInventory entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCellInventory form2Entity(ExpCellInventoryForm form);
}
