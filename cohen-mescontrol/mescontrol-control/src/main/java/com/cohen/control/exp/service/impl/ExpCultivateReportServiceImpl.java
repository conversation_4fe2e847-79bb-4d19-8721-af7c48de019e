package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCultivateReportMapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateReport;
import com.cohen.control.exp.pojo.form.ExpCultivateReportForm;
import com.cohen.control.exp.pojo.po.ExpCultivateReportPO;
import com.cohen.control.exp.pojo.query.ExpCultivateReportPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateReportVO;
import com.cohen.control.exp.service.ExpCultivateReportService;

/**
 * 培养报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCultivateReportServiceImpl extends ServiceImpl<ExpCultivateReportMapper, ExpCultivateReport>implements ExpCultivateReportService {

    private final com.cohen.control.exp.converter.ExpCultivateReportConverter ExpCultivateReportConverter;


    @Override
    public IPage<ExpCultivateReportVO> listExpCultivateReportPages(ExpCultivateReportPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCultivateReportPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCultivateReportPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCultivateReportVO> result = ExpCultivateReportConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCultivateReportVO getExpCultivateReportData(Long id) {
        ExpCultivateReport sd = this.getById(id);
        ExpCultivateReportVO ExpCultivateReportVO = ExpCultivateReportConverter.entity2Vo(sd);
        return ExpCultivateReportVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCultivateReport(ExpCultivateReportForm form) {
        ExpCultivateReport ExpCultivateReport = ExpCultivateReportConverter.form2Entity(form);
        boolean result = this.save(ExpCultivateReport);
        return result;
    }

    @Override
    public boolean updateExpCultivateReport(Long id, ExpCultivateReportForm form) {
        ExpCultivateReport ahDoctor = ExpCultivateReportConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCultivateReport(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
