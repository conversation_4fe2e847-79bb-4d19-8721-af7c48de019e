package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCultivatePlan;
import com.cohen.control.exp.pojo.form.ExpCultivatePlanForm;
import com.cohen.control.exp.pojo.po.ExpCultivatePlanPO;
import com.cohen.control.exp.pojo.vo.ExpCultivatePlanVO;

/**
 * 培养计划转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCultivatePlanConverter {

    ExpCultivatePlanVO po2Vo(ExpCultivatePlanPO po);
    Page<ExpCultivatePlanVO> po2Vo(Page<ExpCultivatePlanPO> po);
    ExpCultivatePlanVO entity2Vo(ExpCultivatePlan entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCultivatePlan form2Entity(ExpCultivatePlanForm form);
}
