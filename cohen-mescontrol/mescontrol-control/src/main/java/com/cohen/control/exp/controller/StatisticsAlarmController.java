package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.control.exp.pojo.entity.StatisticsAlarm;
import com.cohen.control.exp.pojo.form.StatisticsAlarmForm;
import com.cohen.control.exp.pojo.query.StatisticsAlarmPageQuery;
import com.cohen.control.exp.pojo.vo.StatisticsAlarmVO;
import com.cohen.control.exp.service.StatisticsAlarmService;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;


/**
 * 脐带入库单明细Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "告警明细接口")
@RestController
@RequestMapping("/api/v1/exp/statisticsAlarm")
@RequiredArgsConstructor
public class StatisticsAlarmController
{
    private final StatisticsAlarmService statisticsAlarmService;

    @ApiOperation(value = "告警明细分页列表")
    @GetMapping("/pages")
        public PageResult listStatisticsAlarmPages(StatisticsAlarmPageQuery queryParams){
            IPage<StatisticsAlarmVO> result = statisticsAlarmService.listStatisticsAlarmPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "告警明细表单数据")
    @GetMapping("/{id}/form")
    public Result getStatisticsAlarmDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        StatisticsAlarmVO vo = statisticsAlarmService.getStatisticsAlarmData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "处理告警")
    @PutMapping("/handleAlarm")
    public Result getStatisticsAlarmDetail(
            @RequestBody @Valid StatisticsAlarmForm alarm
    ) {
        this.statisticsAlarmService.update(new LambdaUpdateWrapper<StatisticsAlarm>()
                .eq(StatisticsAlarm::getId,alarm.getId())
                .set(StatisticsAlarm::getAlarmStatus,2)
                .set(StatisticsAlarm::getAttachment,alarm.getAttachment())
                .set(StatisticsAlarm::getRemark,alarm.getRemark())
                .set(StatisticsAlarm::getHandlePerson, SecurityUtils.getNickname())
                .set(StatisticsAlarm::getHandleTime,new Date())
        );
        return Result.success();
    }



    @ApiOperation(value = "告警明细")
    @Log(title = "删除脐带入库单明细",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result deleteStatisticsAlarm(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = statisticsAlarmService.deleteStatisticsAlarm(ids);
        return Result.judge(result);
    }


}
