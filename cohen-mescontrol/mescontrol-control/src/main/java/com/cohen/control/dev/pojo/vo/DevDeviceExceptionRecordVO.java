package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备异常记录 回传实体
 * <AUTHOR>
 * @create 2024-10-30
 */
@Data
@ApiModel("设备异常记录回传实体")
public class DevDeviceExceptionRecordVO {
    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("ID")
    private Long id;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 设备编号 */
    @ApiModelProperty("设备编号")
    private String deviceNo;

    /** 异常时间 */
    @ApiModelProperty("异常时间")
    private Date exceptionTime;

    /** 处理时间 */
    @ApiModelProperty("处理时间")
    private Date handledTime;

    /** 处理人 */
    @ApiModelProperty("处理人")
    private String handler;

    /** 异常处理说明 */
    @ApiModelProperty("异常处理说明")
    private String handlerComments;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 扩展字段 */
    @ApiModelProperty("扩展字段")
    private String extra;

    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Integer delFlag;
}
