package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCellOutboundRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellOutboundRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellOutboundRecordVO;
import com.cohen.control.exp.service.ExpCellOutboundRecordService;


/**
 * 细胞出库记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "细胞出库记录接口")
@RestController
@RequestMapping("/api/v1/exp/outrecord")
@RequiredArgsConstructor
public class ExpCellOutboundRecordController
{
    private final ExpCellOutboundRecordService expCellOutboundRecordService;

    @ApiOperation(value = "细胞出库记录分页列表")
    @GetMapping("/pages")
        public PageResult listExpCellOutboundRecordPages(ExpCellOutboundRecordPageQuery queryParams){
            IPage<ExpCellOutboundRecordVO> result = expCellOutboundRecordService.listExpCellOutboundRecordPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "细胞出库记录表单数据")
    @PreAuthorize("@pms.hasPermission('exp:outrecord:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCellOutboundRecordDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCellOutboundRecordVO vo = expCellOutboundRecordService.getExpCellOutboundRecordData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增细胞出库记录")
    @Log(title = "新增细胞出库记录",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:outrecord:add')")
    @PostMapping
    public Result saveExpCellOutboundRecord(
            @RequestBody @Valid ExpCellOutboundRecordForm form
    ) {
        boolean result = expCellOutboundRecordService.saveExpCellOutboundRecord(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改细胞出库记录")
    @Log(title = "修改细胞出库记录",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:outrecord:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCellOutboundRecord(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCellOutboundRecordForm form
    ) {
        boolean result = expCellOutboundRecordService.updateExpCellOutboundRecord(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除细胞出库记录")
    @Log(title = "删除细胞出库记录",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:outrecord:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCellOutboundRecord(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCellOutboundRecordService.deleteExpCellOutboundRecord(ids);
        return Result.judge(result);
    }


}
