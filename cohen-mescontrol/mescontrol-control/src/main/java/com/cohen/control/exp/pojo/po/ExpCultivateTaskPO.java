package com.cohen.control.exp.pojo.po;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 培养任务 数据库映射对象
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
public class ExpCultivateTaskPO{

    private static final long serialVersionUID = 1L;


    /** NULL */
    private Long id;

    /** 步骤号 */

    private Integer stepNumber;

    /** 计划编号 */

    private String planNo;

    /** 任务编号 */

    private String taskNo;

    /** 任务名称（工序名称） */
    private String taskName;

    /** 工序编号 */

    private String processNo;

    /** 工序描述 */

    private String processDescription;

    /** 样品编号 */

    private String sampleNo;

    /** 细胞编号 */

    private String cellNo;

    /** 细胞数量 */

    private String cellQuantity;

    /** 关联设备id */

    private Long deviceId;

    /** 关联设备 */

    private String deviceName;

    /** 负责人id */

    private Long responsiblePersonId;

    /** 负责人名称 */

    private String responsiblePersonName;

    /** 负责人手机号(先做冗余字段) */

    private String responsiblePersonPhone;

    /** 负责人邮箱(先做冗余字段) */

    private String responsiblePersonEmail;

    /** 任务状态(0.待分配 1.待开始 2.待完成 3.已超时 4.已完成 5.已终止) */

    private Integer taskStatus;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionTime;

    /** 完成时长/秒 */

    private String completionDuration;

    /** 任务创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskCreateTime;

    /** 创建操作人 */

    private String taskCreatorName;

    /** 任务分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignmentTime;

    /** 分配操作人 */

    private String assignerName;

    /** 任务终止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminationTime;

    /** 完成操作人 */

    private String completerName;

    /** 终止原因 */

    private String terminationReason;

    /** 终止相关附件 */

    private String terminationAttachment;

    /** 任务完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskCompletionTime;

    /** 检测完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detectionCompletionTime;

    /** 检测时长 */

    private Integer detectionDuration;

    /** 关联报告编号 */

    private String reportNo;

    /** 关联报告id */

    private Long reportId;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createBy;

    /** 修改时间 */
    private Date updateTime;

    /** 修改人 */
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    private Integer delFlag;

    /** 预留字段 */

    private String extra;


}
