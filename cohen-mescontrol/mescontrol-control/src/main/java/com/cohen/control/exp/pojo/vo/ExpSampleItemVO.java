package com.cohen.control.exp.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 脐带入库单明细（脐带管理） 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("脐带入库单明细（脐带管理）回传实体")
public class ExpSampleItemVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 入库单号 */
    @ApiModelProperty("入库单号")
    private String inboundNo;

    /** 入库数量 */
    @ApiModelProperty("入库数量")
    private String num;

    /** 脐带编号 */
    @ApiModelProperty("脐带编号")
    private String sampleNo;

    /** 捐献者名称 */
    @ApiModelProperty("捐献者名称")
    private String donorName;

    /** 捐献者年龄 */
    @ApiModelProperty("捐献者年龄")
    private Integer donorAge;

    /** 健康状态 */
    @ApiModelProperty("健康状态")
    private String healthStatus;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
