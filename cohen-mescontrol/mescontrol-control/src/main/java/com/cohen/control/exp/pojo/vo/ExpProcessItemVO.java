package com.cohen.control.exp.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 工序项 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("工序项回传实体")
public class ExpProcessItemVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 流程id */
    @ApiModelProperty("流程id")
    private Long procedureId;

    /** 流程名称 */
    @ApiModelProperty("流程名称")
    private String procedureName;

    /** 工艺顺序 */
    @ApiModelProperty("工艺顺序")
    private Integer sort;

    /** 工序编号 */
    @ApiModelProperty("工序编号")
    private String no;

    /** 工序名称 */
    @ApiModelProperty("工序名称")
    private String name;

    /** 工序描述 */
    @ApiModelProperty("工序描述")
    private String descirption;

    /** 关联设备id */
    @ApiModelProperty("关联设备id")
    private Long deviceId;

    /** 关联设备 */
    @ApiModelProperty("关联设备")
    private String deviceName;

    /** 设备操作参数参考 */
    @ApiModelProperty("设备操作参数参考")
    private String deviceOperationParameter;

    /** 工序文件名称 */
    @ApiModelProperty("工序文件名称")
    private String fileName;

    /** 任务时长(分钟) */
    @ApiModelProperty("任务时长(分钟)")
    private Integer taskDuration;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;



}
