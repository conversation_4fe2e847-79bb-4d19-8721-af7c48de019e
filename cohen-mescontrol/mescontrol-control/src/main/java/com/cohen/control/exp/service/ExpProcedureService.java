
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpProcedure;
import com.cohen.control.exp.pojo.form.ExpProcedureForm;
import com.cohen.control.exp.pojo.query.ExpProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcedureVO;


/**
 * 工序流程Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpProcedureService extends IService<ExpProcedure> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpProcedureVO> listExpProcedurePages(ExpProcedurePageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpProcedureVO getExpProcedureData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpProcedure(ExpProcedureForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpProcedure(Long id, ExpProcedureForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpProcedure(String idsStr);

    /**
     * 复制工序流程
     * @param id
     * @return
     */
    boolean copy(Long id);
}


