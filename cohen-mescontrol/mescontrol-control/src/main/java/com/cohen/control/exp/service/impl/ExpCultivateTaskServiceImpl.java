package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.common.constant.RedisConstants;
import com.cohen.common.enums.PlanStatusEnum;
import com.cohen.common.enums.TaskStatusEnum;
import com.cohen.common.result.Result;
import com.cohen.common.security.util.SecurityUtils;
import com.cohen.common.web.exception.BizException;
import com.cohen.control.dev.converter.DevConsumableUseRecordConverter;
import com.cohen.control.dev.mapper.DevConsumableUseRecordMapper;
import com.cohen.control.dev.pojo.entity.DevConsumableUseRecord;
import com.cohen.control.dev.pojo.vo.DevConsumableUseRecordVO;
import com.cohen.control.exp.converter.ExpCultivatePlanConverter;
import com.cohen.control.exp.converter.ExpCultivateReportConverter;
import com.cohen.control.exp.converter.ExpSampleConverter;
import com.cohen.control.exp.mapper.ExpCultivatePlanMapper;
import com.cohen.control.exp.mapper.ExpCultivateReportMapper;
import com.cohen.control.exp.mapper.ExpSampleMapper;
import com.cohen.control.exp.pojo.dto.TaskDTO;
import com.cohen.control.exp.pojo.entity.*;
import com.cohen.control.exp.pojo.vo.*;
import com.cohen.control.exp.queue.QueueProducer;
import com.cohen.control.exp.queue.RabbitConstant;
import com.cohen.control.exp.service.ExpCultivateProcedureService;
import com.cohen.system.api.UserFeignClient;
import com.cohen.system.form.AdminForm;
import com.cohen.system.notice.dto.WsQueueMessageDTO;
import com.cohen.system.notice.enums.TerminalTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCultivateTaskMapper;
import com.cohen.control.exp.pojo.form.ExpCultivateTaskForm;
import com.cohen.control.exp.pojo.po.ExpCultivateTaskPO;
import com.cohen.control.exp.pojo.query.ExpCultivateTaskPageQuery;
import com.cohen.control.exp.service.ExpCultivateTaskService;

/**
 * 培养任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service("expCultivateTaskService")
@RequiredArgsConstructor
public class ExpCultivateTaskServiceImpl extends ServiceImpl<ExpCultivateTaskMapper, ExpCultivateTask>implements ExpCultivateTaskService {

    private final com.cohen.control.exp.converter.ExpCultivateTaskConverter ExpCultivateTaskConverter;
    private final ExpCultivatePlanMapper expCultivatePlanMapper;
    private final ExpCultivatePlanConverter expCultivatePlanConverter;
    private final ExpSampleMapper expSampleMapper;
    private final ExpSampleConverter expSampleConverter;
    private final UserFeignClient userFeignClient;
    private final DevConsumableUseRecordMapper consumableUseRecordMapper;
    private final DevConsumableUseRecordConverter consumableUseRecordConverter;
    private final ExpCultivateReportMapper cultivateReportMapper;
    private final ExpCultivateReportConverter cultivateReportConverter;
    private final ExpCultivateProcedureService cultivateProcedureService;
    private final QueueProducer queueProducer;
    private final StringRedisTemplate redisTemplate;


    @Override
    public IPage<ExpCultivateTaskVO> listExpCultivateTaskPages(ExpCultivateTaskPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCultivateTaskPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCultivateTaskPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCultivateTaskVO> result = ExpCultivateTaskConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCultivateTaskVO getExpCultivateTaskData(Long id) {
        ExpCultivateTask sd = this.getById(id);
        ExpCultivateTaskVO ExpCultivateTaskVO = ExpCultivateTaskConverter.entity2Vo(sd);
        return ExpCultivateTaskVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCultivateTask(ExpCultivateTaskForm form) {
        ExpCultivateTask ExpCultivateTask = ExpCultivateTaskConverter.form2Entity(form);
        boolean result = this.save(ExpCultivateTask);
        return result;
    }

    @Override
    public boolean updateExpCultivateTask(Long id, ExpCultivateTaskForm form) {
        ExpCultivateTask ahDoctor = ExpCultivateTaskConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCultivateTask(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }

    /**
     * 分配人员
     * @param form
     * @return
     */
    @Override
    public boolean assignUser(ExpCultivateTaskForm form) {
        boolean result = this.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                .eq(ExpCultivateTask::getId, form.getId())
                .eq(ExpCultivateTask::getTaskStatus,TaskStatusEnum.PENDING_ASSIGNMENT.getValue())
                .set(ExpCultivateTask::getResponsiblePersonId, form.getResponsiblePersonId())
                .set(ExpCultivateTask::getResponsiblePersonName, form.getResponsiblePersonName())
                .set(ExpCultivateTask::getTaskStatus, TaskStatusEnum.PENDING_START.getValue())
                .set(ExpCultivateTask::getAssignmentTime, new Date())
                .set(ExpCultivateTask::getAssignerName, SecurityUtils.getNickname()));
        return result;
    }

    /**
     * 更换人员
     * @param form
     * @return
     */
    @Override
    public boolean changeAssign(ExpCultivateTaskForm form) {
        boolean result = this.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                .eq(ExpCultivateTask::getId, form.getId())
//                .eq(ExpCultivateTask::getTaskStatus,TaskStatusEnum.PENDING_START.getValue())
                .ne(ExpCultivateTask::getTaskStatus,TaskStatusEnum.PENDING_ASSIGNMENT.getValue())
                .set(ExpCultivateTask::getResponsiblePersonId, form.getResponsiblePersonId())
                .set(ExpCultivateTask::getResponsiblePersonName, form.getResponsiblePersonName())
                .set(ExpCultivateTask::getAssignmentTime, new Date())
                .set(ExpCultivateTask::getAssignerName, SecurityUtils.getNickname()));
        return result;
    }

    @Override
    public boolean batchAssign(ExpCultivateTaskForm form) {
        List<Long> taskIdList = form.getTaskIdList();
        if (CollectionUtils.isEmpty(taskIdList)){
            throw new BizException("未选择任务");
        }
        for (Long taskId : taskIdList) {
            ExpCultivateTask cultivateTask = this.getById(taskId);
            if (cultivateTask.getTaskStatus()!=TaskStatusEnum.PENDING_ASSIGNMENT.getValue()){
                throw new BizException("任务（"+cultivateTask.getTaskName()+"）已分配人员，请勿重复分配");
            }
        }

        boolean result = this.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                .in(ExpCultivateTask::getId, form.getTaskIdList())
                .eq(ExpCultivateTask::getTaskStatus,TaskStatusEnum.PENDING_ASSIGNMENT.getValue())
                .set(ExpCultivateTask::getResponsiblePersonId, form.getResponsiblePersonId())
                .set(ExpCultivateTask::getResponsiblePersonName, form.getResponsiblePersonName())
                .set(ExpCultivateTask::getTaskStatus, TaskStatusEnum.PENDING_START.getValue())
                .set(ExpCultivateTask::getAssignmentTime, new Date())
                .set(ExpCultivateTask::getAssignerName, SecurityUtils.getNickname()));
        return result;
    }

    /**
     * 终止任务
     * @param form
     * @return
     */
    @Override
    public boolean terminateTask(ExpCultivateTaskForm form) {
        boolean result = this.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                .eq(ExpCultivateTask::getId, form.getId())
                .set(ExpCultivateTask::getTaskStatus, TaskStatusEnum.TERMINATED.getValue())
                .set(ExpCultivateTask::getTerminationTime, new Date())
                .set(ExpCultivateTask::getTerminationReason, form.getTerminationReason())
                .set(ExpCultivateTask::getTerminationAttachment, form.getTerminationAttachment()));
        return result;
    }

    /**
     * 培养任务表单数据
     * @param id
     * @return
     */
    @Override
    public ExpCultivateTaskInfoVO getExpCultivateTaskDetail(Long id) {
        ExpCultivateTaskInfoVO result = new ExpCultivateTaskInfoVO();
        //查询任务任务
        ExpCultivateTask cultivateTask = this.getById(id);
        ExpCultivateTaskVO taskInfo = ExpCultivateTaskConverter.entity2Vo(cultivateTask);
        //查询当前任务的负责人信息
        if (taskInfo.getResponsiblePersonId()!=null){
            Result<AdminForm> adminFormResult = userFeignClient.getAdminDetail(taskInfo.getResponsiblePersonId());
            if (Result.isSuccess(adminFormResult)&&adminFormResult.getData()!=null){
                taskInfo.setResponsiblePersonName(adminFormResult.getData().getNickname());
                taskInfo.setResponsiblePersonPhone(adminFormResult.getData().getMobile());
                taskInfo.setResponsiblePersonEmail(adminFormResult.getData().getEmail());
                taskInfo.setResponsiblePersonDept(adminFormResult.getData().getDeptName());
            }
        }
        //查询计划信息
        ExpCultivatePlan expCultivatePlan = expCultivatePlanMapper.selectOne(new LambdaQueryWrapper<ExpCultivatePlan>()
                .eq(ExpCultivatePlan::getNo, taskInfo.getPlanNo()));
        ExpCultivatePlanVO planInfo = expCultivatePlanConverter.entity2Vo(expCultivatePlan);

        //查询样品信息
        ExpSample expSample = expSampleMapper.selectOne(new LambdaQueryWrapper<ExpSample>()
                .eq(ExpSample::getSampleNo, taskInfo.getSampleNo()));
        ExpSampleVO sampleInfo = expSampleConverter.entity2Vo(expSample);

        if (cultivateTask.getTaskStatus()==TaskStatusEnum.COMPLETED.getValue()){
            //查询耗材使用记录
            List<DevConsumableUseRecord> consumableUseRecordList = consumableUseRecordMapper.selectList(new LambdaQueryWrapper<DevConsumableUseRecord>()
                    .eq(DevConsumableUseRecord::getTaskNo, taskInfo.getTaskNo()));
            List<DevConsumableUseRecordVO> consumableList = consumableUseRecordConverter.entity2Vo(consumableUseRecordList);

            //查询报告信息
            ExpCultivateReport cultivateReport = cultivateReportMapper.selectOne(new LambdaQueryWrapper<ExpCultivateReport>()
                    .eq(ExpCultivateReport::getTaskNo, taskInfo.getTaskNo()));
            ExpCultivateReportVO reportInfo = cultivateReportConverter.entity2Vo(cultivateReport);

            result.setConsumableList(consumableList);
            result.setReportInfo(reportInfo);
        }
        result.setTaskInfo(taskInfo);
        result.setPlanInfo(planInfo);
        result.setSampleInfo(sampleInfo);
        return result;
    }

    @Override
    public void sendTasksToQueue(String planNo) {
        //查询计划的的工序配置
        List<ExpCultivateProcedure> list = cultivateProcedureService.list(new LambdaQueryWrapper<ExpCultivateProcedure>()
                .eq(ExpCultivateProcedure::getPlanNo, planNo)
                .orderByAsc(ExpCultivateProcedure::getSort));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date currentDate = new Date();
        long currentTimeMillis = currentDate.getTime();
        TaskDTO taskDTO = new TaskDTO();

        for (int i = 0; i < list.size(); i++) {
            ExpCultivateProcedure cultivateProcedure = list.get(i);

            List<ExpCultivateTask> taskList = this.list(new LambdaQueryWrapper<ExpCultivateTask>()
                    .eq(ExpCultivateTask::getProcedureId, cultivateProcedure.getProcessId())
                    .eq(ExpCultivateTask::getPlanNo,planNo)
                    .orderByAsc(ExpCultivateTask::getStepNumber));

            //计算工序流程的开始时间和当前的毫秒差
            long delayTimes=cultivateProcedure.getStartTime().getTime()-currentTimeMillis;

            taskDTO.setPlanId(cultivateProcedure.getPlanId());
            taskDTO.setPlanNo(cultivateProcedure.getPlanNo());
            taskDTO.setPlanName(cultivateProcedure.getPlanName());
            taskDTO.setProcedureId(cultivateProcedure.getProcessId());

            //能进入该sendTasksToQueue方法说明已经到达计划中第一个任务的开始时间了
            if (i==0){
                //更新计划状态
                expCultivatePlanMapper.update(null,new LambdaUpdateWrapper<ExpCultivatePlan>()
                        .eq(ExpCultivatePlan::getNo, planNo)
                        .set(ExpCultivatePlan::getStatus, PlanStatusEnum.IN_PROGRESS.getValue())
                        .set(ExpCultivatePlan::getStartTime,currentDate));
                //更新该计划第一个任务的状态
                this.update(new LambdaUpdateWrapper<ExpCultivateTask>()
                        .eq(ExpCultivateTask::getId, taskList.get(0).getId())
                        .eq(ExpCultivateTask::getTaskNo, taskList.get(0).getTaskNo())
                        .set(ExpCultivateTask::getTaskStatus, TaskStatusEnum.PENDING_COMPLETION.getValue())
                        .set(ExpCultivateTask::getStartTime,currentDate));
                redisTemplate.opsForValue().set(RedisConstants.PLAN_TASK_STEP+cultivateProcedure.getPlanId(),"0");
            }

            //循环任务，计算每个任务的开始时间、结束时间与当前时间的时间差
            for (int j = 0; j < taskList.size(); j++) {
                ExpCultivateTask task = taskList.get(j);
                //任务的执行时长
                delayTimes += task.getTaskDuration()*60*1000;

                taskDTO.setTaskId(task.getId());
                taskDTO.setTaskNo(task.getTaskNo());
                taskDTO.setTaskName(task.getTaskName());
                taskDTO.setDeviceId(task.getDeviceId());
                taskDTO.setDeviceName(task.getDeviceName());
                taskDTO.setStepNumber(task.getStepNumber());

                String message = JSON.toJSONString(taskDTO);

                System.out.println("当前时间===》"+sdf.format(currentDate));
                System.out.println("消息延迟时间===》"+delayTimes);
                //将任务发送到MQ--任务结束
                queueProducer.sendMessage(message, RabbitConstant.DELAY_EXCHANGE,RabbitConstant.DELAY_ROUTING_KEY,delayTimes);

                // 如果有下一个任务，计算下一个任务的开始时间
                if (j+1<taskList.size()){
                    //获取下一个任务
                    ExpCultivateTask nextTask = taskList.get(j+1);
                    //下一个任务的开始时间则为当前任务的结束时间
                    taskDTO.setTaskId(nextTask.getId());
                    taskDTO.setTaskNo(nextTask.getTaskNo());
                    taskDTO.setTaskName(nextTask.getTaskName());
                    taskDTO.setDeviceId(nextTask.getDeviceId());
                    taskDTO.setDeviceName(nextTask.getDeviceName());
                    taskDTO.setStepNumber(nextTask.getStepNumber());

                    String taskMessage = JSON.toJSONString(taskDTO);

                    System.out.println("任务====》" + nextTask.getTaskNo() + "   距离开始时间====》" + delayTimes);
                    //发送消息到mq -- 任务开始
                    queueProducer.sendMessage(taskMessage, RabbitConstant.TASK_START_DELAY_EXCHANGE, RabbitConstant.TASK_START_DELAY_ROUTING_KEY, delayTimes);

                }
            }
        }
    }
}
