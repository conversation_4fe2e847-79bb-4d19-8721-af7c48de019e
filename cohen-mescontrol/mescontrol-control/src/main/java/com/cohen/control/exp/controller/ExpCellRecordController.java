package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCellRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellRecordVO;
import com.cohen.control.exp.service.ExpCellRecordService;


/**
 * 细胞记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "细胞记录接口")
@RestController
@RequestMapping("/api/v1/exp/record")
@RequiredArgsConstructor
public class ExpCellRecordController
{
    private final ExpCellRecordService expCellRecordService;

    @ApiOperation(value = "细胞记录分页列表")
    @GetMapping("/pages")
        public PageResult listExpCellRecordPages(ExpCellRecordPageQuery queryParams){
            IPage<ExpCellRecordVO> result = expCellRecordService.listExpCellRecordPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "细胞记录表单数据")
    @PreAuthorize("@pms.hasPermission('exp:record:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCellRecordDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCellRecordVO vo = expCellRecordService.getExpCellRecordData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增细胞记录")
    @Log(title = "新增细胞记录",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:record:add')")
    @PostMapping
    public Result saveExpCellRecord(
            @RequestBody @Valid ExpCellRecordForm form
    ) {
        boolean result = expCellRecordService.saveExpCellRecord(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改细胞记录")
    @Log(title = "修改细胞记录",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:record:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCellRecord(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCellRecordForm form
    ) {
        boolean result = expCellRecordService.updateExpCellRecord(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除细胞记录")
    @Log(title = "删除细胞记录",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:record:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCellRecord(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCellRecordService.deleteExpCellRecord(ids);
        return Result.judge(result);
    }


}
