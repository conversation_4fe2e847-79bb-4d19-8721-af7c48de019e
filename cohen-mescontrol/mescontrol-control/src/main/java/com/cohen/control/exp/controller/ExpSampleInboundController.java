package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpSampleInboundForm;
import com.cohen.control.exp.pojo.query.ExpSampleInboundPageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleInboundVO;
import com.cohen.control.exp.service.ExpSampleInboundService;


/**
 * 脐带入库单Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "脐带入库单接口")
@RestController
@RequestMapping("/api/v1/exp/inbound")
@RequiredArgsConstructor
public class ExpSampleInboundController
{
    private final ExpSampleInboundService expSampleInboundService;

    @ApiOperation(value = "脐带入库单分页列表")
    @GetMapping("/pages")
        public PageResult listExpSampleInboundPages(ExpSampleInboundPageQuery queryParams){
            IPage<ExpSampleInboundVO> result = expSampleInboundService.listExpSampleInboundPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "脐带入库单表单数据")
    @PreAuthorize("@pms.hasPermission('exp:inbound:detail')")
    @GetMapping("/{id}/form")
    public Result getExpSampleInboundDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpSampleInboundVO vo = expSampleInboundService.getExpSampleInboundData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增脐带入库单")
    @Log(title = "新增脐带入库单",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:inbound:add')")
    @PostMapping
    public Result saveExpSampleInbound(
            @RequestBody @Valid ExpSampleInboundForm form
    ) {
        boolean result = expSampleInboundService.saveExpSampleInbound(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改脐带入库单")
    @Log(title = "修改脐带入库单",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:inbound:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpSampleInbound(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpSampleInboundForm form
    ) {
        boolean result = expSampleInboundService.updateExpSampleInbound(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除脐带入库单")
    @Log(title = "删除脐带入库单",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:inbound:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpSampleInbound(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expSampleInboundService.deleteExpSampleInbound(ids);
        return Result.judge(result);
    }


}
