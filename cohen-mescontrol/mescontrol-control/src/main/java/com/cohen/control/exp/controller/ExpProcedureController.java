package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpProcedureForm;
import com.cohen.control.exp.pojo.query.ExpProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpProcedureVO;
import com.cohen.control.exp.service.ExpProcedureService;


/**
 * 工序流程Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "工序流程接口")
@RestController
@RequestMapping("/api/v1/exp/procedure")
@RequiredArgsConstructor
public class ExpProcedureController
{
    private final ExpProcedureService expProcedureService;

    @ApiOperation(value = "工序流程分页列表")
    @GetMapping("/pages")
        public PageResult listExpProcedurePages(ExpProcedurePageQuery queryParams){
            IPage<ExpProcedureVO> result = expProcedureService.listExpProcedurePages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "工序流程表单数据")
    @PreAuthorize("@pms.hasPermission('exp:procedure:detail')")
    @GetMapping("/{id}/form")
    public Result getExpProcedureDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpProcedureVO vo = expProcedureService.getExpProcedureData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增工序流程")
    @Log(title = "新增工序流程",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:procedure:add')")
    @PostMapping
    public Result saveExpProcedure(
            @RequestBody @Valid ExpProcedureForm form
    ) {
        boolean result = expProcedureService.saveExpProcedure(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改工序流程")
    @Log(title = "修改工序流程",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:procedure:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpProcedure(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpProcedureForm form
    ) {
        boolean result = expProcedureService.updateExpProcedure(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除工序流程")
    @Log(title = "删除工序流程",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:procedure:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpProcedure(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expProcedureService.deleteExpProcedure(ids);
        return Result.judge(result);
    }

    @ApiOperation(value = "复制工序流程")
    @Log(title = "复制工序流程",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:procedure:copy')")
    @PostMapping("/copy/{id}")
    public Result copy(@PathVariable Long id){
        boolean result = expProcedureService.copy(id);
        return Result.judge(result);
    }

}
