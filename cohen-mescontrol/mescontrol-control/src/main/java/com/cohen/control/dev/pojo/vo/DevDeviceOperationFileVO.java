package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 设备操作文件 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("设备操作文件回传实体")
public class DevDeviceOperationFileVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 关联设备id */
    @ApiModelProperty("关联设备id")
    private Long deviceId;

    /** 设备编号 */
    @ApiModelProperty("设备编号")
    private String deviceNo;

    /** 关联设备 */
    @ApiModelProperty("关联设备")
    private String deviceName;

    /** 参数描述 */
    @ApiModelProperty("参数描述")
    private String parameterDescription;

    /** 文件访问路径 */
    @ApiModelProperty("文件访问路径")
    private String fileAccessPath;

    /** 文件名称 */
    @ApiModelProperty("文件名称")
    private String fileName;

    /** 文件绝对路径 */
    @ApiModelProperty("文件绝对路径")
    private String fileAbsolutePath;

    /** 文件大小 */
    @ApiModelProperty("文件大小")
    private String fileSize;

    /** 下载次数 */
    @ApiModelProperty("下载次数")
    private Integer downloadCount;

    /** 查看次数 */
    @ApiModelProperty("查看次数")
    private Integer viewCount;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
