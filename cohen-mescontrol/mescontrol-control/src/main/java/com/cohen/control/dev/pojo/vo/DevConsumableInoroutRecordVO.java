package com.cohen.control.dev.pojo.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 耗材出入库明细 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材出入库明细回传实体")
public class DevConsumableInoroutRecordVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 耗材编码 */
    @ApiModelProperty("耗材编码")
    private String code;

    /** 耗材基础信息id */
    @ApiModelProperty("耗材基础信息id")
    private Long consumableId;

    /** 耗材名称 */
    @ApiModelProperty("耗材名称")
    private String consumableName;

    /** 出入库仓库id */
    @ApiModelProperty("出入库仓库id")
    private Long warehouseId;

    /** 出入库仓库 */
    @ApiModelProperty("出入库仓库")
    private String warehouseName;

    /** 出入库类型 */
    @ApiModelProperty("出入库类型")
    private Integer type;

    /** 业务id */
    @ApiModelProperty("业务id")
    private Long businessId;

    /** 业务名称 */
    @ApiModelProperty("业务名称")
    private String businessName;

    /** 数量 */
    @ApiModelProperty("数量")
    private Integer num;

    /** 单价 */
    @ApiModelProperty("单价")
    private BigDecimal unit;

    /** 总金额 */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    /** 操作日期 */
    @ApiModelProperty("操作日期")
    private Date operatorTime;

    /** 操作人员 */
    @ApiModelProperty("操作人员")
    private String operator;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
