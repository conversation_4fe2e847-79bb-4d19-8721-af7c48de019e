package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpSampleItemForm;
import com.cohen.control.exp.pojo.query.ExpSampleItemPageQuery;
import com.cohen.control.exp.pojo.vo.ExpSampleItemVO;
import com.cohen.control.exp.service.ExpSampleItemService;


/**
 * 脐带入库单明细Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "脐带入库单明细接口")
@RestController
@RequestMapping("/api/v1/exp/sampleitem")
@RequiredArgsConstructor
public class ExpSampleItemController
{
    private final ExpSampleItemService expSampleItemService;

    @ApiOperation(value = "脐带入库单明细分页列表")
    @GetMapping("/pages")
        public PageResult listExpSampleItemPages(ExpSampleItemPageQuery queryParams){
            IPage<ExpSampleItemVO> result = expSampleItemService.listExpSampleItemPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "脐带入库单明细表单数据")
    @PreAuthorize("@pms.hasPermission('exp:sampleitem:detail')")
    @GetMapping("/{id}/form")
    public Result getExpSampleItemDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpSampleItemVO vo = expSampleItemService.getExpSampleItemData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增脐带入库单明细")
    @Log(title = "新增脐带入库单明细",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:sampleitem:add')")
    @PostMapping
    public Result saveExpSampleItem(
            @RequestBody @Valid ExpSampleItemForm form
    ) {
        boolean result = expSampleItemService.saveExpSampleItem(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改脐带入库单明细")
    @Log(title = "修改脐带入库单明细",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:sampleitem:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpSampleItem(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpSampleItemForm form
    ) {
        boolean result = expSampleItemService.updateExpSampleItem(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除脐带入库单明细")
    @Log(title = "删除脐带入库单明细",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:sampleitem:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpSampleItem(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expSampleItemService.deleteExpSampleItem(ids);
        return Result.judge(result);
    }


}
