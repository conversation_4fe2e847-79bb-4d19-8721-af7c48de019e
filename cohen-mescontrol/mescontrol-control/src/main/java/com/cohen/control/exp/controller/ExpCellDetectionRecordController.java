package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import com.cohen.control.exp.pojo.form.ExpCellDetectionRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellDetectionRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellDetectionRecordVO;
import com.cohen.control.exp.service.ExpCellDetectionRecordService;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 细胞记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "细胞记录接口")
@RestController
@RequestMapping("/api/v1/exp/cell/detection/record")
@RequiredArgsConstructor
public class ExpCellDetectionRecordController
{
    private final ExpCellDetectionRecordService expCellDetectionRecordService;

    @ApiOperation(value = "细胞检测记录分页列表")
    @GetMapping("/pages")
        public PageResult listExpCellDetectionRecordPages(ExpCellDetectionRecordPageQuery queryParams){
            IPage<ExpCellDetectionRecordVO> result = expCellDetectionRecordService.listExpCellDetectionRecordPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "细胞检测记录表单数据")
    @GetMapping("/{id}/form")
    public Result getExpCellDetectionRecordDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCellDetectionRecordVO vo = expCellDetectionRecordService.getExpCellDetectionRecordData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增细胞检测记录")
    @Log(title = "新增细胞记录",businessType = BusinessType.INSERT)
    @PostMapping
    public Result saveExpCellDetectionRecord(
            @RequestBody @Valid ExpCellDetectionRecordForm form
    ) {
        boolean result = expCellDetectionRecordService.saveExpCellDetectionRecord(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改细胞检测记录")
    @Log(title = "修改细胞记录",businessType = BusinessType.UPDATE)
    @PutMapping(value = "/{id}")
    public Result updateExpCellDetectionRecord(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCellDetectionRecordForm form
    ) {
        boolean result = expCellDetectionRecordService.updateExpCellDetectionRecord(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除细胞检测记录")
    @Log(title = "删除细胞记录",businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result deleteExpCellDetectionRecord(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCellDetectionRecordService.deleteExpCellDetectionRecord(ids);
        return Result.judge(result);
    }


}
