package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cohen.control.exp.converter.ExpSampleConverter;
import com.cohen.control.exp.pojo.entity.ExpCellInventory;
import com.cohen.control.exp.pojo.entity.ExpSample;
import com.cohen.control.exp.pojo.form.ExpCellOutboundRecordForm;
import com.cohen.control.exp.pojo.vo.ExpSampleVO;
import com.cohen.control.exp.service.ExpCellOutboundRecordService;
import com.cohen.control.exp.service.ExpSampleService;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCellInventoryForm;
import com.cohen.control.exp.pojo.query.ExpCellInventoryPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellInventoryVO;
import com.cohen.control.exp.service.ExpCellInventoryService;

import java.util.Date;


/**
 * 细胞库存Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "细胞库存接口")
@RestController
@RequestMapping("/api/v1/exp/inventory")
@RequiredArgsConstructor
public class ExpCellInventoryController
{
    private final ExpCellInventoryService expCellInventoryService;

    private final ExpCellOutboundRecordService outboundRecordService;

    private final ExpSampleService sampleService;

    private final ExpSampleConverter expSampleConverter;

    @ApiOperation(value = "细胞库存分页列表")
    @GetMapping("/pages")
        public PageResult listExpCellInventoryPages(ExpCellInventoryPageQuery queryParams){
            IPage<ExpCellInventoryVO> result = expCellInventoryService.listExpCellInventoryPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "细胞库存表单数据")
    @PreAuthorize("@pms.hasPermission('exp:inventory:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCellInventoryDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCellInventoryVO vo = expCellInventoryService.getExpCellInventoryData(id);

        ExpSample expSample = sampleService.getOne(
                new LambdaQueryWrapper<ExpSample>()
                        .eq(ExpSample::getSampleNo, vo.getSampleNo())
        );
        if (expSample != null) {
            ExpSampleVO sampleVO = expSampleConverter.entity2Vo(expSample);
            vo.setSampleVO(sampleVO);
        }else{
            //为空不展示
        }
        return Result.success(vo);
    }

    @ApiOperation(value = "新增细胞库存")
    @Log(title = "新增细胞库存",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:inventory:add')")
    @PostMapping
    public Result saveExpCellInventory(
            @RequestBody @Valid ExpCellInventoryForm form
    ) {
        boolean result = expCellInventoryService.saveExpCellInventory(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改细胞库存")
    @Log(title = "修改细胞库存",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:inventory:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCellInventory(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCellInventoryForm form
    ) {
        boolean result = expCellInventoryService.updateExpCellInventory(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除细胞库存")
    @Log(title = "删除细胞库存",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:inventory:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCellInventory(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCellInventoryService.deleteExpCellInventory(ids);
        return Result.judge(result);
    }

    @ApiOperation(value = "细胞出库")
    @Log(title = "细胞出库",businessType = BusinessType.UPDATE)
    @PutMapping(value = "/cellOutbound")
    public Result outBoundCellInventory( @RequestBody ExpCellOutboundRecordForm form) {
        //修改细胞库存状态
        expCellInventoryService.update(new LambdaUpdateWrapper<ExpCellInventory>()
                .eq(ExpCellInventory::getCellNo,form.getCellNo())
                .set(ExpCellInventory::getOutboundStatus,2)
                .set(ExpCellInventory::getOutboundTime,new Date())
        );
        boolean result = outboundRecordService.saveExpCellOutboundRecord(form);
        return Result.judge(result);
    }



}
