package com.cohen.control.dev.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 设备使用记录 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("设备使用记录回传实体")
public class DevDeviceRecordVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 设备编号 */
    @ApiModelProperty("设备编号")
    private String deviceNo;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /** 所属类型id */
    @ApiModelProperty("所属类型id")
    private Long deviceTypeId;

    /** 设备类型名称 */

    private String deviceTypeName;

    /** 所属品牌（冗余） */
    @ApiModelProperty("所属品牌（冗余）")
    private String brand;

    /** 生产厂家（冗余） */
    @ApiModelProperty("生产厂家（冗余）")
    private String manufacturer;

    /** 操作类型 */
    @ApiModelProperty("操作类型")
    private String operatorType;

    /** 操作人 */
    @ApiModelProperty("操作人")
    private String operator;

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    private Date operatorTime;

    /** 操作结果(1.入库 2.正常 3.异常) */
    @ApiModelProperty("操作结果(1.入库 2.正常 3.异常)")
    private String operatorResult;

    /** 任务编号 */
    @ApiModelProperty("任务编号")
    private String taskNo;

    /** 计划编号 */
    @ApiModelProperty("计划编号")
    private String planNo;

    /** 样品编号 */
    @ApiModelProperty("样品编号")
    private String sampleNo;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 扩展字段 */
    @ApiModelProperty("扩展字段")
    private String extra;

    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Integer delFlag;


}
