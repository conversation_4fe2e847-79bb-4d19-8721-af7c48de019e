package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpProcessItem;
import com.cohen.control.exp.pojo.form.ExpProcessItemForm;
import com.cohen.control.exp.pojo.po.ExpProcessItemPO;
import com.cohen.control.exp.pojo.vo.ExpProcessItemVO;

import java.util.List;

/**
 * 工序项转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpProcessItemConverter {

    ExpProcessItemVO po2Vo(ExpProcessItemPO po);
    Page<ExpProcessItemVO> po2Vo(Page<ExpProcessItemPO> po);
    ExpProcessItemVO entity2Vo(ExpProcessItem entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpProcessItem form2Entity(ExpProcessItemForm form);

    List<ExpProcessItem> form2Entity(List<ExpProcessItemForm> processItemList);

    List<ExpProcessItemVO> entity2Vo(List<ExpProcessItem> processItemList);
}
