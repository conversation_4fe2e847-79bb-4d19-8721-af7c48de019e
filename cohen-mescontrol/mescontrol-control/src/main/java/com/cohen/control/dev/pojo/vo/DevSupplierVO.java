package com.cohen.control.dev.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 供应商 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("供应商回传实体")
public class DevSupplierVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 名称 */
    @ApiModelProperty("名称")
    private String name;

    /** 联系人 */
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String contactPhone;

    /** 合作日期 */
    @ApiModelProperty("合作日期")
    private Date cooperationDate;

    /** 合作标签 */
    @ApiModelProperty("合作标签")
    private String cooperationTag;

    /** 状态 */
    @ApiModelProperty("状态")
    private Integer status;

    /** 区域编码 */
    @ApiModelProperty("区域编码")
    private String regionCode;

    /** 区域 */
    @ApiModelProperty("区域")
    private String region;

    /** 地址 */
    @ApiModelProperty("地址")
    private String description;

    /** 显示顺序 */
    @ApiModelProperty("显示顺序")
    private Integer sort;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
