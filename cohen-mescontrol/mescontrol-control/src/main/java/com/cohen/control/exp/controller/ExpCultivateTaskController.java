package com.cohen.control.exp.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cohen.common.enums.TaskStatusEnum;
import com.cohen.control.exp.pojo.entity.ExpCultivateTask;
import com.cohen.control.exp.pojo.vo.ExpCultivateTaskInfoVO;
import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCultivateTaskForm;
import com.cohen.control.exp.pojo.query.ExpCultivateTaskPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateTaskVO;
import com.cohen.control.exp.service.ExpCultivateTaskService;


/**
 * 培养任务Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "培养任务接口")
@RestController
@RequestMapping("/api/v1/exp/task")
@RequiredArgsConstructor
public class ExpCultivateTaskController
{
    private final ExpCultivateTaskService expCultivateTaskService;

    @ApiOperation(value = "培养任务分页列表")
    @GetMapping("/pages")
        public PageResult listExpCultivateTaskPages(ExpCultivateTaskPageQuery queryParams){
            IPage<ExpCultivateTaskVO> result = expCultivateTaskService.listExpCultivateTaskPages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "培养任务表单数据")
    @PreAuthorize("@pms.hasPermission('exp:task:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCultivateTaskDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
//        ExpCultivateTaskVO vo = expCultivateTaskService.getExpCultivateTaskData(id);
        ExpCultivateTaskInfoVO result = expCultivateTaskService.getExpCultivateTaskDetail(id);
        return Result.success(result);
    }

    @ApiOperation(value = "新增培养任务")
    @Log(title = "新增培养任务",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:task:add')")
    @PostMapping
    public Result saveExpCultivateTask(
            @RequestBody @Valid ExpCultivateTaskForm form
    ) {
        boolean result = expCultivateTaskService.saveExpCultivateTask(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改培养任务")
    @Log(title = "修改培养任务",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:task:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCultivateTask(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCultivateTaskForm form
    ) {
        boolean result = expCultivateTaskService.updateExpCultivateTask(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除培养任务")
    @Log(title = "删除培养任务",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:task:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCultivateTask(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCultivateTaskService.deleteExpCultivateTask(ids);
        return Result.judge(result);
    }

    @ApiOperation(value = "分配人员")
    @Log(title = "分配人员",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:task:assign')")
    @PutMapping("/assign")
    public Result assign(@RequestBody ExpCultivateTaskForm form){
        boolean result = expCultivateTaskService.assignUser(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "更换人员")
    @Log(title = "更换人员",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:task:changeAssign')")
    @PutMapping("/changeAssign")
    public Result changeAssign(@RequestBody ExpCultivateTaskForm form){
        boolean result = expCultivateTaskService.changeAssign(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "批量分配人员")
    @Log(title = "批量分配人员",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:task:batchAssign')")
    @PutMapping("/batchAssign")
    public Result batchAssign(@RequestBody ExpCultivateTaskForm form){
        boolean result = expCultivateTaskService.batchAssign(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "终止任务")
    @Log(title = "终止任务",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:task:terminateTask')")
    @PutMapping("/terminateTask")
    public Result terminateTask(@RequestBody ExpCultivateTaskForm form){
        boolean result = expCultivateTaskService.terminateTask(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "查询任务超时数量")
    @GetMapping("/getTimeoutCount")
    public Result getTimeoutCount(){
        int count = expCultivateTaskService.count(new LambdaQueryWrapper<ExpCultivateTask>()
                .eq(ExpCultivateTask::getTaskStatus, TaskStatusEnum.TIMEOUT.getValue()));
        return Result.success(count);
    }

}
