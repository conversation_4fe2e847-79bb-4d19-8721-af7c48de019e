
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCellOutboundRecord;
import com.cohen.control.exp.pojo.form.ExpCellOutboundRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellOutboundRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellOutboundRecordVO;


/**
 * 细胞出库记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCellOutboundRecordService extends IService<ExpCellOutboundRecord> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCellOutboundRecordVO> listExpCellOutboundRecordPages(ExpCellOutboundRecordPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCellOutboundRecordVO getExpCellOutboundRecordData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCellOutboundRecord(ExpCellOutboundRecordForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCellOutboundRecord(Long id, ExpCellOutboundRecordForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCellOutboundRecord(String idsStr);
}


