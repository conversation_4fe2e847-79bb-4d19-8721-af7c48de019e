package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保养规则 回传实体
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("保养规则回传实体")
public class DevMaintenanceRulesVO {

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 名称 */
    @ApiModelProperty("名称")
    private String name;

    /** 设备id */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /** 规则内容 */
    @ApiModelProperty("规则内容")
    private String ruleContent;

    /** 保养周期 */
    @ApiModelProperty("保养周期")
    private String maintenanceCycle;

    /** 保养间隔 */
    @ApiModelProperty("保养间隔")
    private String maintenanceInterval;

    /** 提前推送天数 */
    @ApiModelProperty("提前推送天数")
    private Integer advanceNotificationDays;

    /** 推送时间 */
    @ApiModelProperty("推送时间")
    private Date notificationTime;

    /** 启用状态(0禁用 1正常) */
    @ApiModelProperty("启用状态(0禁用 1正常)")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


}
