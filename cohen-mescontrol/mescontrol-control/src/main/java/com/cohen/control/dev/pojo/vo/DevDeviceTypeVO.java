package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 设备类型 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("设备类型回传实体")
public class DevDeviceTypeVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 类型名称 */
    @ApiModelProperty("类型名称")
    private String name;

    /** 所属上级 */
    @ApiModelProperty("所属上级")
    private Long parent;

    /** 祖级id */
    @ApiModelProperty("祖级id")
    private String acester;

    /** 排序 */
    @ApiModelProperty("排序")
    private Integer ancestors;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 状态(1.启用 0.禁用) */
    @ApiModelProperty("状态(1.启用 0.禁用)")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 扩展字段 */
    @ApiModelProperty("扩展字段")
    private String extra;

    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Integer delFlag;

    private Integer sort;

    //父节点名称
    private String parentName;

    List<DevDeviceTypeVO> children;

}
