package com.cohen.control.dev.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 设备基础信息 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("设备基础信息回传实体")
public class DevDeviceInfoVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String name;

    /** 类型id */
    @ApiModelProperty("类型id")
    private Long typeId;

    /** 类型名称 */
    @ApiModelProperty("类型名称")
    private String typeName;

    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String model;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 设备品牌 */
    @ApiModelProperty("设备品牌")
    private String brand;

    /** 生产厂家 */
    @ApiModelProperty("生产厂家")
    private String manufacturer;

    /** 设备图片 */
    @ApiModelProperty("设备图片")
    private String equipmentPictures;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 参数配置 */
    @ApiModelProperty("参数配置")
    private String paramConfig;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 扩展字段 */
    @ApiModelProperty("扩展字段")
    private String extra;

    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Integer delFlag;


}
