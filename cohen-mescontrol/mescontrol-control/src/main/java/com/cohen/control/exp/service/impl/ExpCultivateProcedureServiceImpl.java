package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCultivateProcedureMapper;
import com.cohen.control.exp.pojo.entity.ExpCultivateProcedure;
import com.cohen.control.exp.pojo.form.ExpCultivateProcedureForm;
import com.cohen.control.exp.pojo.po.ExpCultivateProcedurePO;
import com.cohen.control.exp.pojo.query.ExpCultivateProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateProcedureVO;
import com.cohen.control.exp.service.ExpCultivateProcedureService;

/**
 * 培养计划绑定工序流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCultivateProcedureServiceImpl extends ServiceImpl<ExpCultivateProcedureMapper, ExpCultivateProcedure>implements ExpCultivateProcedureService {

    private final com.cohen.control.exp.converter.ExpCultivateProcedureConverter ExpCultivateProcedureConverter;


    @Override
    public IPage<ExpCultivateProcedureVO> listExpCultivateProcedurePages(ExpCultivateProcedurePageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCultivateProcedurePO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCultivateProcedurePO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCultivateProcedureVO> result = ExpCultivateProcedureConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCultivateProcedureVO getExpCultivateProcedureData(Long id) {
        ExpCultivateProcedure sd = this.getById(id);
        ExpCultivateProcedureVO ExpCultivateProcedureVO = ExpCultivateProcedureConverter.entity2Vo(sd);
        return ExpCultivateProcedureVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCultivateProcedure(ExpCultivateProcedureForm form) {
        ExpCultivateProcedure ExpCultivateProcedure = ExpCultivateProcedureConverter.form2Entity(form);
        boolean result = this.save(ExpCultivateProcedure);
        return result;
    }

    @Override
    public boolean updateExpCultivateProcedure(Long id, ExpCultivateProcedureForm form) {
        ExpCultivateProcedure ahDoctor = ExpCultivateProcedureConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCultivateProcedure(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
