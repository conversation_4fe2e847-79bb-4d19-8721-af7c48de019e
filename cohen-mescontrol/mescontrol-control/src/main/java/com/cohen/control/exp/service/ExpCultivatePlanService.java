
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.job.exception.TaskException;
import com.cohen.control.exp.pojo.dto.TaskDTO;
import com.cohen.control.exp.pojo.entity.ExpCultivatePlan;
import com.cohen.control.exp.pojo.form.ExpCultivatePlanForm;
import com.cohen.control.exp.pojo.query.ExpCultivatePlanPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivatePlanVO;
import org.quartz.SchedulerException;

import java.util.Map;


/**
 * 培养计划Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCultivatePlanService extends IService<ExpCultivatePlan> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCultivatePlanVO> listExpCultivatePlanPages(ExpCultivatePlanPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCultivatePlanVO getExpCultivatePlanData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCultivatePlan(ExpCultivatePlanForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCultivatePlan(Long id, ExpCultivatePlanForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCultivatePlan(String idsStr) throws SchedulerException;

    /**
     * 终止培养计划
     * @param form
     * @return
     */
    boolean terminatePlan(ExpCultivatePlanForm form);

    /**
     * 培养计划绑定样品
     * @param form
     * @return
     */
    boolean bindSample(ExpCultivatePlanForm form);

    /**
     * 查询计划和设备参数
     * @param processId
     * @return
     */
    Map<String, Object> getPlanAndDeviceParam(Long processId);


    void  pushDeviceParamsDataWebSocket(TaskDTO taskDTO);

    void  pushStepWebSocket(TaskDTO taskDTO);

    void  pushPlanWebSocket(TaskDTO taskDTO);
}


