
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.ExpCellRecord;
import com.cohen.control.exp.pojo.form.ExpCellRecordForm;
import com.cohen.control.exp.pojo.query.ExpCellRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellRecordVO;


/**
 * 细胞记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface ExpCellRecordService extends IService<ExpCellRecord> {

    /**
     * 分頁列表
     * @return
     */
    IPage<ExpCellRecordVO> listExpCellRecordPages(ExpCellRecordPageQuery queryParams);

    /**
     * 获取详情数据
     *
     * @param id
     * @return
     */
    ExpCellRecordVO getExpCellRecordData(Long id);

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    boolean saveExpCellRecord(ExpCellRecordForm form);

    /**
     * 修改
     *
     * @param id   ID
     * @param form 表单对象
     * @return
     */
    boolean updateExpCellRecord(Long id, ExpCellRecordForm form);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteExpCellRecord(String idsStr);
}


