package com.cohen.control.exp.controller;

import com.cohen.operlog.annot.Log;
import com.cohen.operlog.enums.BusinessType;
import com.cohen.common.result.PageResult;
import com.cohen.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.Valid;
import com.cohen.control.exp.pojo.form.ExpCultivateProcedureForm;
import com.cohen.control.exp.pojo.query.ExpCultivateProcedurePageQuery;
import com.cohen.control.exp.pojo.vo.ExpCultivateProcedureVO;
import com.cohen.control.exp.service.ExpCultivateProcedureService;


/**
 * 培养计划绑定工序流程Controller
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Api(tags = "培养计划绑定工序流程接口")
@RestController
@RequestMapping("/api/v1/exp/cultivateprocedure")
@RequiredArgsConstructor
public class ExpCultivateProcedureController
{
    private final ExpCultivateProcedureService expCultivateProcedureService;

    @ApiOperation(value = "培养计划绑定工序流程分页列表")
    @GetMapping("/pages")
        public PageResult listExpCultivateProcedurePages(ExpCultivateProcedurePageQuery queryParams){
            IPage<ExpCultivateProcedureVO> result = expCultivateProcedureService.listExpCultivateProcedurePages(queryParams);
            return PageResult.success(result);
        }

    @ApiOperation(value = "培养计划绑定工序流程表单数据")
    @PreAuthorize("@pms.hasPermission('exp:cultivateprocedure:detail')")
    @GetMapping("/{id}/form")
    public Result getExpCultivateProcedureDetail(
            @ApiParam(value = "ID") @PathVariable Long id
    ) {
        ExpCultivateProcedureVO vo = expCultivateProcedureService.getExpCultivateProcedureData(id);
        return Result.success(vo);
    }

    @ApiOperation(value = "新增培养计划绑定工序流程")
    @Log(title = "新增培养计划绑定工序流程",businessType = BusinessType.INSERT)
    @PreAuthorize("@pms.hasPermission('exp:cultivateprocedure:add')")
    @PostMapping
    public Result saveExpCultivateProcedure(
            @RequestBody @Valid ExpCultivateProcedureForm form
    ) {
        boolean result = expCultivateProcedureService.saveExpCultivateProcedure(form);
        return Result.judge(result);
    }

    @ApiOperation(value = "修改培养计划绑定工序流程")
    @Log(title = "修改培养计划绑定工序流程",businessType = BusinessType.UPDATE)
    @PreAuthorize("@pms.hasPermission('exp:cultivateprocedure:update')")
    @PutMapping(value = "/{id}")
    public Result updateExpCultivateProcedure(
            @ApiParam("id") @PathVariable Long id,
            @RequestBody @Validated ExpCultivateProcedureForm form
    ) {
        boolean result = expCultivateProcedureService.updateExpCultivateProcedure(id,form);
        return Result.judge(result);
    }

    @ApiOperation(value = "删除培养计划绑定工序流程")
    @Log(title = "删除培养计划绑定工序流程",businessType = BusinessType.DELETE)
    @PreAuthorize("@pms.hasPermission('exp:cultivateprocedure:del')")
    @DeleteMapping("/{ids}")
    public Result deleteExpCultivateProcedure(
            @ApiParam("ID，多个以英文逗号(,)分割") @PathVariable String ids
    ) {
        boolean result = expCultivateProcedureService.deleteExpCultivateProcedure(ids);
        return Result.judge(result);
    }


}
