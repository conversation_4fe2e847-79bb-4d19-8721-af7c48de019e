
package com.cohen.control.exp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.control.exp.pojo.entity.StatisticsAlarm;
import com.cohen.control.exp.pojo.query.StatisticsAlarmPageQuery;
import com.cohen.control.exp.pojo.vo.StatisticsAlarmVO;
import com.cohen.control.exp.pojo.vo.IndexCountAllVO;


/**
 * 脐带管理 Service接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */

public interface StatisticsAlarmService extends IService<StatisticsAlarm> {

    /**
     * 分頁列表
     * @return
     */
    IPage<StatisticsAlarmVO> listStatisticsAlarmPages(StatisticsAlarmPageQuery queryParams);


    IndexCountAllVO indexCount();

    /**
     * 获取详情数据

     *
     * @param id
     * @return
     */
    StatisticsAlarmVO getStatisticsAlarmData(Long id);


    /**
     * 删除
     *
     * @param idsStr ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteStatisticsAlarm(String idsStr);
}


