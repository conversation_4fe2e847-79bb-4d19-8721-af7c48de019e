package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.cohen.control.exp.mapper.ExpCellOutboundRecordMapper;
import com.cohen.control.exp.pojo.entity.ExpCellOutboundRecord;
import com.cohen.control.exp.pojo.form.ExpCellOutboundRecordForm;
import com.cohen.control.exp.pojo.po.ExpCellOutboundRecordPO;
import com.cohen.control.exp.pojo.query.ExpCellOutboundRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellOutboundRecordVO;
import com.cohen.control.exp.service.ExpCellOutboundRecordService;

/**
 * 细胞出库记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCellOutboundRecordServiceImpl extends ServiceImpl<ExpCellOutboundRecordMapper, ExpCellOutboundRecord>implements ExpCellOutboundRecordService {

    private final com.cohen.control.exp.converter.ExpCellOutboundRecordConverter ExpCellOutboundRecordConverter;


    @Override
    public IPage<ExpCellOutboundRecordVO> listExpCellOutboundRecordPages(ExpCellOutboundRecordPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCellOutboundRecordPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCellOutboundRecordPO> PoPage = this.baseMapper.listPages(page, queryParams);

        // 实体转换
        Page<ExpCellOutboundRecordVO> result = ExpCellOutboundRecordConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCellOutboundRecordVO getExpCellOutboundRecordData(Long id) {
        ExpCellOutboundRecord sd = this.getById(id);
        ExpCellOutboundRecordVO ExpCellOutboundRecordVO = ExpCellOutboundRecordConverter.entity2Vo(sd);
        return ExpCellOutboundRecordVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCellOutboundRecord(ExpCellOutboundRecordForm form) {
        ExpCellOutboundRecord expCellOutboundRecord = ExpCellOutboundRecordConverter.form2Entity(form);
        expCellOutboundRecord.setOutboundTime(new Date());
        boolean result = this.save(expCellOutboundRecord);
        return result;
    }

    @Override
    public boolean updateExpCellOutboundRecord(Long id, ExpCellOutboundRecordForm form) {
        ExpCellOutboundRecord ahDoctor = ExpCellOutboundRecordConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCellOutboundRecord(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
