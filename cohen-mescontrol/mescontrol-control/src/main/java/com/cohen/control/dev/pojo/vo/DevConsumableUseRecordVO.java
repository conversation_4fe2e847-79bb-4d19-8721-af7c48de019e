package com.cohen.control.dev.pojo.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 耗材使用记录 回传实体
 * 
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ApiModel("耗材使用记录回传实体")
public class DevConsumableUseRecordVO{

    private static final long serialVersionUID = 1L;

    /** NULL */
    @ApiModelProperty("NULL")
    private Long id;

    /** 耗材编码 */
    @ApiModelProperty("耗材编码")
    private String code;

    /** 计划编号 */
    @ApiModelProperty("计划编号")
    private String planNo;

    /** 计划名称 */
    @ApiModelProperty("计划名称")
    private String planName;

    /** 任务编号 */
    @ApiModelProperty("任务编号")
    private String taskNo;

    /** 任务名称 */
    @ApiModelProperty("任务名称")
    private String taskName;

    /** 耗材名称 */
    @ApiModelProperty("耗材名称")
    private String consumableName;

    /** 耗材编码 */
    @ApiModelProperty("耗材编码")
    private String consumableCode;

    /** 使用数量 */
    @ApiModelProperty("使用数量")
    private Integer num;

    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String model;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 所属批次 */
    @ApiModelProperty("所属批次")
    private String batchNumber;

    /** 操作类型 */
    @ApiModelProperty("操作类型")
    private String operatorType;

    /** 操作人 */
    @ApiModelProperty("操作人")
    private String operator;

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    private Date operatorTime;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updateBy;

    /** 是否删除(1删除 0未删除) */
    @ApiModelProperty("是否删除(1删除 0未删除)")
    private Integer delFlag;

    /** 预留字段 */
    @ApiModelProperty("预留字段")
    private String extra;


    private String consumableTypeName;

}
