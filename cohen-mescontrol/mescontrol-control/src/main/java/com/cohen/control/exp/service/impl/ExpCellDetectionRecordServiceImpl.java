package com.cohen.control.exp.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cohen.control.exp.converter.ExpCellDetectionRecordConverter;
import com.cohen.control.exp.mapper.ExpCellDetectionRecordMapper;
import com.cohen.control.exp.pojo.entity.ExpCellDetectionRecord;
import com.cohen.control.exp.pojo.form.ExpCellDetectionRecordForm;
import com.cohen.control.exp.pojo.po.ExpCellDetectionRecordPO;
import com.cohen.control.exp.pojo.query.ExpCellDetectionRecordPageQuery;
import com.cohen.control.exp.pojo.vo.ExpCellDetectionRecordVO;
import com.cohen.control.exp.service.ExpCellDetectionRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 细胞记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Service
@RequiredArgsConstructor
public class ExpCellDetectionRecordServiceImpl extends ServiceImpl<ExpCellDetectionRecordMapper, ExpCellDetectionRecord>implements ExpCellDetectionRecordService {

    private final com.cohen.control.exp.converter.ExpCellDetectionRecordConverter expCellDetectionRecordConverter;


    @Override
    public IPage<ExpCellDetectionRecordVO> listExpCellDetectionRecordPages(ExpCellDetectionRecordPageQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<ExpCellDetectionRecordPO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<ExpCellDetectionRecordPO> PoPage = this.baseMapper.getDetectionList(page, queryParams);

        // 实体转换
        Page<ExpCellDetectionRecordVO> result = expCellDetectionRecordConverter.po2Vo(PoPage);

        return result;
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @Override
    public ExpCellDetectionRecordVO getExpCellDetectionRecordData(Long id) {
        ExpCellDetectionRecord sd = this.getById(id);
        ExpCellDetectionRecordVO ExpCellDetectionRecordVO = expCellDetectionRecordConverter.entity2Vo(sd);
        return ExpCellDetectionRecordVO;
    }

    /**
     * 新增
     *
     * @param form 表单对象
     * @return
     */
    @Override
    public boolean saveExpCellDetectionRecord(ExpCellDetectionRecordForm form) {
        ExpCellDetectionRecord ExpCellDetectionRecord = expCellDetectionRecordConverter.form2Entity(form);
        boolean result = this.save(ExpCellDetectionRecord);
        return result;
    }

    @Override
    public boolean updateExpCellDetectionRecord(Long id, ExpCellDetectionRecordForm form) {
        ExpCellDetectionRecord ahDoctor = expCellDetectionRecordConverter.form2Entity(form);
        boolean result = this.updateById(ahDoctor);
        return result;
    }

    @Override
    public boolean deleteExpCellDetectionRecord(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的数据为空");
        // 逻辑删除
        List<Long> ids = Arrays.asList(idsStr.split(",")).stream()
                .map(idStr -> Long.parseLong(idStr)).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}
