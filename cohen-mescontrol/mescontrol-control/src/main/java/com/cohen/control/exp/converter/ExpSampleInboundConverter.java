package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpSampleInbound;
import com.cohen.control.exp.pojo.form.ExpSampleInboundForm;
import com.cohen.control.exp.pojo.po.ExpSampleInboundPO;
import com.cohen.control.exp.pojo.vo.ExpSampleInboundVO;

/**
 * 脐带入库单转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpSampleInboundConverter {

    ExpSampleInboundVO po2Vo(ExpSampleInboundPO po);
    Page<ExpSampleInboundVO> po2Vo(Page<ExpSampleInboundPO> po);
    ExpSampleInboundVO entity2Vo(ExpSampleInbound entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpSampleInbound form2Entity(ExpSampleInboundForm form);
}
