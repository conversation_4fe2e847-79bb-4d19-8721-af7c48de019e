package com.cohen.control.exp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.cohen.control.exp.pojo.entity.ExpCellRecord;
import com.cohen.control.exp.pojo.form.ExpCellRecordForm;
import com.cohen.control.exp.pojo.po.ExpCellRecordPO;
import com.cohen.control.exp.pojo.vo.ExpCellRecordVO;

/**
 * 细胞记录转换类
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper(componentModel = "spring")
public interface ExpCellRecordConverter {

    ExpCellRecordVO po2Vo(ExpCellRecordPO po);
    Page<ExpCellRecordVO> po2Vo(Page<ExpCellRecordPO> po);
    ExpCellRecordVO entity2Vo(ExpCellRecord entity);

    @InheritInverseConfiguration(name = "entity2Form")
    ExpCellRecord form2Entity(ExpCellRecordForm form);
}
