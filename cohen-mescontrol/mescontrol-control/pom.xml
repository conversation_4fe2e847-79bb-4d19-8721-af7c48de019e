<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cohen-mescontrol</artifactId>
        <groupId>com.cohen</groupId>
        <version>1.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mescontrol-control</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-operLog</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-mybatis</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-file</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-security</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-log</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-core</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!-- 单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring Cloud & Alibaba -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 注册中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <!-- 配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- openfeign依赖 1. http客户端选择okhttp 2. loadbalancer替换ribbon -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Sentinel流量控制、熔断降级 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <!-- Sentinel规则持久化至Nacos配置 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!-- 分布式对象存储 -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>


        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>system-api</artifactId>
            <version>1.1.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-web</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>mescontrol-core</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>common-rabbitmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cohen</groupId>
            <artifactId>system-notice-api</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- Quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.masmgc</groupId>
            <artifactId>masmgc-sdk-mms</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <!--打包必备-->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>