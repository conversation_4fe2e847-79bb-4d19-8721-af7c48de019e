package com.cohen;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(basePackages = {"com.cohen.control.**","com.cohen.common.security.**"})
@MapperScan(basePackages = {"com.cohen.**.mapper"})
@EnableFeignClients(basePackages = {"com.cohen.**.api"})
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling
public class MesControlGrabApplication {

	public static void main(String[] args) {
		SpringApplication.run(MesControlGrabApplication.class, args);
		// 添加Bouncy Castle作为安全提供程序
		//Security.addProvider(new BouncyCastleProvider());
	}

}
