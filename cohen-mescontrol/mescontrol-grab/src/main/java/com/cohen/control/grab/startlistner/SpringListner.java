package com.cohen.control.grab.startlistner;


import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;

@Component
public class SpringListner implements CommandLineRunner {
    private int port  = 7702;

    @Override
    public void run(String... args) throws Exception {

        try (ServerSocket serverSocket = new ServerSocket(port)) {
            System.out.println("服务端已启动，等待连接...");
            Socket clientSocket = serverSocket.accept(); // 等待网关连接
            System.out.println("客户端连接成功，开始接收数据...");
            // 获取输入流读取数据
            BufferedReader reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("接收到数据: " + line);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
