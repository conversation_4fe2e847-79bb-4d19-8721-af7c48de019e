package com.cohen.notice;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.cohen.operlog.**", "com.cohen.notice.**"})
@MapperScan(basePackages = {"com.cohen.**.mapper"})
public class SystemNoticeApplication {

	public static void main(String[] args) {
		SpringApplication.run(SystemNoticeApplication.class, args);
	}

}
