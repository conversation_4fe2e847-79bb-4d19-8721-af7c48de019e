package com.cohen.system;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@ComponentScan(basePackages={"com.cohen.operlog.*","com.cohen.system.*"})
@MapperScan(basePackages = {"com.cohen.**.mapper"})
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.cohen.**.api"})
public class SystemApplication {
    public static void main(String[] args) {
        try {
            SpringApplication.run(SystemApplication.class, args);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
