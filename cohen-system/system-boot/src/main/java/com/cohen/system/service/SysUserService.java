package com.cohen.system.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cohen.common.file.vo.FileInfo;
import com.cohen.system.dto.UserAuthInfo;
import com.cohen.system.form.InstitutionUserForm;
import com.cohen.system.pojo.bo.UserAnalysisBO;
import com.cohen.system.pojo.dto.UserImportDTO;
import com.cohen.system.pojo.entity.SysUser;
import com.cohen.system.pojo.form.ClientUserForm;
import com.cohen.system.pojo.form.UserForm;
import com.cohen.system.pojo.form.user.RecoverPassword;
import com.cohen.system.pojo.form.user.RegisterUserForm;
import com.cohen.system.pojo.form.user.UserMobileForm;
import com.cohen.system.pojo.form.user.UserPasswordForm;
import com.cohen.system.pojo.query.UserAnalysisPageQuery;
import com.cohen.system.pojo.query.UserPageQuery;
import com.cohen.system.pojo.vo.user.UserExportVO;
import com.cohen.system.pojo.vo.user.UserLoginVO;
import com.cohen.system.pojo.vo.user.UserVO;

import java.io.IOException;
import java.util.List;

/**
 * 用户业务接口
 *
 * <AUTHOR>
 * @Date 2023-10-09 15:00
 * @Version 1.0.0
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 用户分页列表
     *
     * @return
     */
    IPage<UserVO> listUserPages(UserPageQuery queryParams);


    /**
     * 获取用户详情
     *
     * @param userId
     * @return
     */
    UserForm getUserFormData(Long userId);


    /**
     * 新增用户
     *
     * @param userForm 用户表单对象
     * @return
     */
    boolean saveUser(UserForm userForm);

    /**
     * 修改用户
     *
     * @param userId   用户ID
     * @param userForm 用户表单对象
     * @return
     */
    boolean updateUser(Long userId, UserForm userForm);


    /**
     * 删除用户
     *
     * @param idsStr 用户ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteUsers(String idsStr);


    /**
     * 修改用户密码
     *
     * @param userId   用户ID
     * @param password 用户密码
     * @return
     */
    boolean updatePassword(Long userId, String password);

    /**
     * 根据用户名获取认证信息
     *
     * @param username
     * @return
     */
    UserAuthInfo getUserAuthInfo(String username);

    /**
     * 根据手机号获取认证信息
     *
     * @param moblie
     * @return
     */
    UserAuthInfo getUserAuthInfoByMobile(String moblie);

    /**
     * 导入用户
     *
     * @param userImportDTO
     * @return
     */
    String importUsers(UserImportDTO userImportDTO) throws IOException;

    /**
     * 获取导出用户列表
     *
     * @param queryParams
     * @return
     */
    List<UserExportVO> listExportUsers(UserPageQuery queryParams);


    /**
     * 获取登录用户信息
     *
     * @return
     */
    UserLoginVO getLoginUserInfo();

    UserLoginVO getLoginUserInfoById(Long id);

    String getAllUserIDbyMenberId(Long uid,String mids);

    String getUserIDbyCommunityId(Long cid);

    UserLoginVO getLoginUserInfo2(String username);

    /**
     * 新增子用户 商户和社区
     *
     * @param userForm 用户表单对象
     * @return
     */
    boolean saveClientUser(ClientUserForm userForm);

    /**
     * 查询业务员用户
     * @param menberIds
     * @param queryParams
     * @return
     */
    IPage<UserVO> findUserPages( UserPageQuery queryParams);

    /**
     * 根据用户名强制退出
     * @param username
     * @return
     */
    boolean forceLogout(String username);


    /**
     * 注册机构用户
     * @param userForm
     * @return
     */
    boolean registerUser(RegisterUserForm userForm);

    /**
     * 将用户信息复制到机构用户中
     * @param userId
     * @return
     */
    InstitutionUserForm copyUserToInstitutionUser(Long userId);

    /**
     * 用户修改密码
     * @param passwordForm
     * @return
     */
    boolean updateUserPassword(UserPasswordForm passwordForm);

    /**
     * 新增个人用户
     * @param userForm
     * @return
     */
    Long addClientUser(UserForm userForm);

    /**
     * 修改个人用户信息
     * @param userId
     * @param userForm
     * @return
     */
    boolean updateClientUser(Long userId, UserForm userForm);

    /**
     * 查询未认证的用户分页列表
     * @param queryParams
     * @return
     */
    IPage<UserVO> listUnverifiedUsers(UserPageQuery queryParams);


    Integer countUnverifiedUser(UserPageQuery queryParams);

    /**
     * 修改用户头像头像
     * @param userForm
     * @return
     */
    boolean updateUserAvatar(UserForm userForm);

    /**
     * 更换手机号
     * @param userMobileForm
     * @return
     */
    boolean updateMobile(UserMobileForm userMobileForm);

    /**
     * 查询个人和机构用户信息
     * @param queryParams
     * @return
     */
    IPage<UserVO> getUserPages(UserPageQuery queryParams);

    /**
     * 用户分页列表
     *
     * @return
     */
    IPage<UserAnalysisBO> listUserAnalysisPages(UserAnalysisPageQuery queryParams);

    List<UserAnalysisBO> listExportUsersAnalysis(UserAnalysisPageQuery queryParams);

    /**
     * 找回密码
     * @param recoverPassword
     * @return
     */
    boolean recoverPassword(RecoverPassword recoverPassword);

    /**
     * 平台邀请邀请二维码(药店、合伙人)
     * @param type
     * @return
     */
    FileInfo invitationCode(Integer type)throws IOException ;
}
